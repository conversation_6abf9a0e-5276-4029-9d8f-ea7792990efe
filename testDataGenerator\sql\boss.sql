/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : boss

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:25:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_roles
-- ----------------------------
DROP TABLE IF EXISTS `t_roles`;
CREATE TABLE `t_roles`  (
  `id` char(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `data` json NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_users
-- ----------------------------
DROP TABLE IF EXISTS `t_users`;
CREATE TABLE `t_users`  (
  `id` char(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `data` json NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
