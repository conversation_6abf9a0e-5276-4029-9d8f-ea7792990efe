/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : sheet

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:37:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_archive
-- ----------------------------
DROP TABLE IF EXISTS `t_archive`;
CREATE TABLE `t_archive`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '归档记录唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '归档名称',
  `DATA` blob NULL COMMENT '归档数据内容',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储归档数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_archive_record
-- ----------------------------
DROP TABLE IF EXISTS `t_archive_record`;
CREATE TABLE `t_archive_record`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '归档记录唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `ARCHIVE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '归档ID，关联t_archive表',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '记录名称',
  `DATA` blob NULL COMMENT '记录的具体数据内容',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储归档记录的具体数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_carbon_copy
-- ----------------------------
DROP TABLE IF EXISTS `t_carbon_copy`;
CREATE TABLE `t_carbon_copy`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '抄送记录唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `TASK_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务ID',
  `ACTIVITY_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '活动ID',
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '抄送的用户ID',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `PROCESS_DESIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计ID',
  `PROCESS_DESIGN_INSTANCE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例ID',
  `PROCESS_DESIGN_INSTANCE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程实例名称',
  `RECEIPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据ID',
  `RECEIPT_SERIAL_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据序列号',
  `CREATOR` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '抄送创建者',
  `OWNER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '抄送的所有者',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_carbon_copy_TENANT_ID_IDX`(`TENANT_ID`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储流程抄送数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_domain_field
-- ----------------------------
DROP TABLE IF EXISTS `t_domain_field`;
CREATE TABLE `t_domain_field`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '全局字段ID',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '全局字段名称',
  `CODE` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '全局字段编号',
  `COMPONENT_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段类型',
  `DATA` blob NULL COMMENT '字段数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_domain_field_TENANT_ID_IDX`(`TENANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '全局字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_done
-- ----------------------------
DROP TABLE IF EXISTS `t_done`;
CREATE TABLE `t_done`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '已完成任务唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `VOTE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '投票ID',
  `USER_TASK_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户任务ID',
  `ACTIVITY_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '活动ID',
  `TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务类型',
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完成任务的用户ID',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务创建时间',
  `COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '任务完成时间',
  `PROCESS_DESIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计ID',
  `PROCESS_DESIGN_INSTANCE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例ID',
  `PROCESS_DESIGN_INSTANCE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程实例名称',
  `RECEIPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据ID',
  `RECEIPT_SERIAL_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据序列号',
  `CREATOR` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务创建者',
  `OWNER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务所有者',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储已完成的流程任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_form_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_form_definition`;
CREATE TABLE `t_form_definition`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单定义唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单名称',
  `CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单编码',
  `SHEET_DEFINITION_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单结构定义ID',
  `UPDATE_TIME_COLUMN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间字段',
  `CREATED_TIME_COLUMN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间字段',
  `SERIAL_NO_COLUMN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '序列号字段',
  `DISABLE_COLUMN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '禁用字段',
  `FIELDS` blob NULL COMMENT '表单字段定义',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储表单定义' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_form_design
-- ----------------------------
DROP TABLE IF EXISTS `t_form_design`;
CREATE TABLE `t_form_design`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单设计唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单设计名称',
  `TAGS` json NULL COMMENT '表单设计标签',
  `DATA` blob NULL COMMENT '表单设计数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储表单设计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_process_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_process_definition`;
CREATE TABLE `t_process_definition`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '流程定义唯一标识',
  `PROPS` blob NULL COMMENT '流程属性',
  `DATA` blob NULL COMMENT '流程定义数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储流程定义' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_process_design
-- ----------------------------
DROP TABLE IF EXISTS `t_process_design`;
CREATE TABLE `t_process_design`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '流程设计唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '流程创建时间',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计名称',
  `CREATOR` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计创建者',
  `PROPS` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '流程属性',
  `DATA` blob NULL COMMENT '流程设计数据',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储流程设计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_process_design_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_process_design_instance`;
CREATE TABLE `t_process_design_instance`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '流程设计实例唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `PROCESS_DESIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例名称',
  `CREATOR` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程实例创建者',
  `CREATOR_DEPT` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者部门',
  `OWNER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程实例所有者',
  `CURRENT_USER_TASK_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '当前用户任务ID',
  `EXECUTION_LOGS` blob NULL COMMENT '执行日志',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '流程实例创建时间',
  `COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '流程实例完成时间',
  `STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程状态',
  `FORM_DESIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单设计ID',
  `FORM_DEFINITION_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单定义ID',
  `FORM_RECORD_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单记录ID',
  `RECEIPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据ID',
  `RECEIPT_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据类型',
  `RECEIPT_SERIAL_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据序列号',
  `DATA` blob NULL COMMENT '流程实例数据',
  `update_time` datetime NULL DEFAULT NULL COMMENT '流程实例更新时间',
  `deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_process_design_instance_PROCESS_DESIGN_ID_IDX`(`PROCESS_DESIGN_ID`) USING BTREE,
  INDEX `t_process_design_instance_RECEIPT_ID_IDX`(`RECEIPT_ID`) USING BTREE,
  INDEX `t_process_design_instance_RECEIPT_TYPE_IDX`(`RECEIPT_TYPE`) USING BTREE,
  INDEX `t_process_design_instance_update_time_IDX`(`update_time`, `CREATE_TIME`) USING BTREE,
  INDEX `t_process_design_instance_TENANT_ID_IDX`(`TENANT_ID`) USING BTREE,
  INDEX `t_process_design_instance_CREATOR_IDX`(`CREATOR`) USING BTREE,
  INDEX `t_process_design_instance_CREATE_TIME_IDX`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储流程设计实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_process_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_process_instance`;
CREATE TABLE `t_process_instance`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '流程实例唯一标识',
  `PROCESS_DEFINITION_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程定义ID',
  `START` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
  `DONE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完成时间',
  `CURRENT_ACTIVITY_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '当前活动ID',
  `CURRENT_TASK_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '当前任务ID',
  `PROPS` blob NULL COMMENT '流程属性',
  `DATA` blob NULL COMMENT '流程实例数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储流程实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_receipt
-- ----------------------------
DROP TABLE IF EXISTS `t_receipt`;
CREATE TABLE `t_receipt`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据唯一标识',
  `SERIAL_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编号迭代器ID',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据名称',
  `INVISIBLE` tinyint(4) NULL DEFAULT NULL COMMENT '单据是否可见',
  `GROUP_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据分组ID',
  `TAGS` blob NULL COMMENT '单据标签',
  `RECEIPT_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据类型',
  `CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据系统编号',
  `FORM_DEFINITION_ID` varbinary(50) NULL DEFAULT NULL COMMENT '表单定义编号',
  `DELETED` tinyint(4) NULL DEFAULT NULL COMMENT '是否删除',
  `DISABLED` tinyint(4) NULL DEFAULT NULL COMMENT '是否禁用',
  `SORT` int(11) NULL DEFAULT NULL COMMENT '排序字段',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `DATA` blob NULL COMMENT '序列化数据',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_receipt_DISABLED_IDX`(`DISABLED`) USING BTREE,
  INDEX `t_receipt_FORM_DEFINITION_ID_IDX`(`FORM_DEFINITION_ID`) USING BTREE,
  INDEX `t_receipt_NAME_IDX`(`NAME`) USING BTREE,
  INDEX `t_receipt_RECEIPT_TYPE_IDX`(`RECEIPT_TYPE`) USING BTREE,
  INDEX `t_receipt_TENANT_ID_IDX`(`TENANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '单据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_receipt_design
-- ----------------------------
DROP TABLE IF EXISTS `t_receipt_design`;
CREATE TABLE `t_receipt_design`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据设计唯一标识',
  `RECEIPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据ID',
  `DATA` blob NULL COMMENT '单据设计数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储单据设计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_receipt_group
-- ----------------------------
DROP TABLE IF EXISTS `t_receipt_group`;
CREATE TABLE `t_receipt_group`  (
  `ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据分组ID',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组名称',
  `DATA` blob NULL COMMENT '分组数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_receipt_group_TENANT_ID_IDX`(`TENANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储单据分组信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_serial_format
-- ----------------------------
DROP TABLE IF EXISTS `t_serial_format`;
CREATE TABLE `t_serial_format`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列号格式ID',
  `FORMAT` blob NULL COMMENT '序列号格式数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储序列号格式' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_serial_sequence
-- ----------------------------
DROP TABLE IF EXISTS `t_serial_sequence`;
CREATE TABLE `t_serial_sequence`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列号ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '序列号名称',
  `SERIAL` bigint(20) NULL DEFAULT NULL COMMENT '当前序列号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储序列号序列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sheet_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_sheet_definition`;
CREATE TABLE `t_sheet_definition`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单定义ID',
  `VERSION` int(11) NULL DEFAULT NULL COMMENT '表单版本号',
  `COLUMNS` blob NULL COMMENT '列定义',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '表单定义' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sheet_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sheet_record`;
CREATE TABLE `t_sheet_record`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单记录ID',
  `DEFINITION_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单定义ID',
  `VERSION` int(11) NULL DEFAULT NULL COMMENT '表单版本',
  `DATA` json NULL COMMENT '表单记录数据',
  `VALUE_PROPS` blob NULL COMMENT '数据类型描述',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_definition_id`(`DEFINITION_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '表单元数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_task_model
-- ----------------------------
DROP TABLE IF EXISTS `t_task_model`;
CREATE TABLE `t_task_model`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务模型ID',
  `USERS` blob NULL COMMENT '任务相关用户数据',
  `DATA` blob NULL COMMENT '任务模型数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储任务模型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_todo
-- ----------------------------
DROP TABLE IF EXISTS `t_todo`;
CREATE TABLE `t_todo`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '待办任务唯一标识',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `VOTE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '投票ID',
  `USER_TASK_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户任务ID',
  `ACTIVITY_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '活动ID',
  `TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务类型',
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '待办任务的用户ID',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务创建时间',
  `CHECKING` tinyint(4) NULL DEFAULT NULL COMMENT '任务是否正在核对',
  `REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务备注',
  `PROCESS_DESIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计ID',
  `PROCESS_DESIGN_INSTANCE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例ID',
  `PROCESS_DESIGN_INSTANCE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例名称',
  `RECEIPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据ID',
  `RECEIPT_SERIAL_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据序列号',
  `CREATOR` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务创建者',
  `OWNER` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务所有者',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_todo_TENANT_ID_IDX`(`TENANT_ID`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储待办任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_task
-- ----------------------------
DROP TABLE IF EXISTS `t_user_task`;
CREATE TABLE `t_user_task`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户任务ID',
  `TENANT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
  `PROCESS_DESIGN_INSTANCE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流程设计实例ID',
  `ACTIVITY_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '活动ID',
  `USER_TASK_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户任务类型',
  `USERS` json NULL COMMENT '用户信息',
  `DATA` blob NULL COMMENT '任务数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '存储用户任务' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
