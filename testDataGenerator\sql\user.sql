/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : user

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:38:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_auth_history
-- ----------------------------
DROP TABLE IF EXISTS `t_auth_history`;
CREATE TABLE `t_auth_history`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ACTION_RATE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACTION_SIMILARITY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CARD_NO` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BEHIND_ID_CARD_ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `CELLPHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `DELIVERY_CODE` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELIVERY_MSG` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_MSG` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FACE_SIMILARITY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FRONT_ID_CARD_ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORDER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PHOTO_ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `SEQ_NO` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `VIDEO_STR` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_IDCARD_NO`(`ID_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_card
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_card`;
CREATE TABLE `t_bank_card`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIND_TIME` datetime NULL DEFAULT NULL,
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRIMARY_CARD` bit(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_IDCARD_NO`(`ID_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_card_bin
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_card_bin`;
CREATE TABLE `t_bank_card_bin`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_BIN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_LENGTH` int(11) NULL DEFAULT NULL,
  `CARD_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BIN_LENGTH`(`CARD_BIN`, `CARD_LENGTH`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_card_bin_0729
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_card_bin_0729`;
CREATE TABLE `t_bank_card_bin_0729`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_BIN` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_LENGTH` int(11) NULL DEFAULT NULL,
  `CARD_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BIN_LENGTH`(`CARD_BIN`, `CARD_LENGTH`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info`;
CREATE TABLE `t_bank_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SHORT_BANK_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BANK_CODE`(`BANK_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info0113
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info0113`;
CREATE TABLE `t_bank_info0113`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SHORT_BANK_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BANK_CODE`(`BANK_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info_0729
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info_0729`;
CREATE TABLE `t_bank_info_0729`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SHORT_BANK_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BANK_CODE`(`BANK_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `t_extend_info`;
CREATE TABLE `t_extend_info`  (
  `CATEGORY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `USER_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `DATA` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  PRIMARY KEY (`CATEGORY`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_extend_info_category
-- ----------------------------
DROP TABLE IF EXISTS `t_extend_info_category`;
CREATE TABLE `t_extend_info_category`  (
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_external_user
-- ----------------------------
DROP TABLE IF EXISTS `t_external_user`;
CREATE TABLE `t_external_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BIND_ID` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_USER_BIND_ID`(`BIND_ID`) USING BTREE,
  INDEX `I_USER_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_limit2_ocr_info
-- ----------------------------
DROP TABLE IF EXISTS `t_limit2_ocr_info`;
CREATE TABLE `t_limit2_ocr_info`  (
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BIRTH_ADDRESS` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTHDAY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FRONTAGE_IMG` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_CONFIRMED` bit(1) NULL DEFAULT b'0',
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_SIGNING_ORGANS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_VALIDITY_PERIOD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATION` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REVERSE_SIDE_IMG` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEX` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_IDCARD_NO`(`ID_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_natural_person
-- ----------------------------
DROP TABLE IF EXISTS `t_natural_person`;
CREATE TABLE `t_natural_person`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_PERSON`(`ID_CARD_NO`, `NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_ocr_info
-- ----------------------------
DROP TABLE IF EXISTS `t_ocr_info`;
CREATE TABLE `t_ocr_info`  (
  `USER_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `BIRTH_ADDRESS` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTHDAY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FRONTAGE_IMG` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_CONFIRMED` bit(1) NULL DEFAULT b'0',
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_SIGNING_ORGANS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_VALIDITY_PERIOD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATION` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REVERSE_SIDE_IMG` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEX` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`USER_ID`) USING BTREE,
  INDEX `IDX_IDCARD_NO`(`ID_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personal_income
-- ----------------------------
DROP TABLE IF EXISTS `t_personal_income`;
CREATE TABLE `t_personal_income`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APP_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CAPITAL_CHANNEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOCIAL_CREDIT_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INCOME` decimal(19, 2) NULL DEFAULT NULL,
  `ACCOUNT_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCOUNT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INCOME_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_TIME` datetime NULL DEFAULT NULL,
  `CRED_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NOTE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PLATFORM_USER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `LAST_MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `INDEX_APP_CODE_SEQ_NO`(`APP_CODE`, `SEQ_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol`;
CREATE TABLE `t_protocol`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PROTOCOL_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PROTOCOL_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PROTOCOL_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PUBLISHER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SHOW_ORDER` int(11) NULL DEFAULT NULL,
  `FORCE_ORDER` int(11) NULL DEFAULT NULL,
  `IS_SHOW` bit(1) NULL DEFAULT NULL,
  `IS_FORCE_READ` bit(1) NULL DEFAULT NULL,
  `REMARK` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROTOCOL_CONTENT` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `SIGNTURE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VALIDITY_START` datetime NULL DEFAULT NULL,
  `VALIDITY_END` datetime NULL DEFAULT NULL,
  `PUBLISH_TIME` datetime NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `PROTOCOL_IDX`(`PROTOCOL_STATUS`, `PROTOCOL_TYPE`, `PROTOCOL_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol_user_real
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol_user_real`;
CREATE TABLE `t_protocol_user_real`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PROTOCOL_ID` bigint(20) NOT NULL,
  `USER_ID` bigint(20) NOT NULL,
  `IS_CONFIRM` bit(1) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `PROTOCOL_IDX_PROTOCOL_USER_ID`(`PROTOCOL_ID`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '交换区名称',
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '路由key',
  `PAYLOAD` blob NULL COMMENT '消息体',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `FAIL_COUNT` int(11) NULL DEFAULT NULL COMMENT '失败次数',
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL COMMENT '最后一次失败时间',
  `PROPERTIES` blob NULL COMMENT '属性',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '系统消息队列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOCKED_TIME` datetime NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE,
  INDEX `I_SYS_MQ_LOG_TIME`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `CELL_PHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMAIL` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_AUTH` bit(1) NOT NULL,
  `IS_ENABLE` bit(1) NOT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REAL_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATURAL_ID` bigint(20) NULL DEFAULT NULL,
  `AUTH_MODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '认证模式',
  `REMARK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `DEFAULT_OPERATE_SPACE_ID` bigint(20) NULL DEFAULT NULL,
  `OPERATE_SPACE_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LAST_LOGIN_SPACE_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CELL_PHONE`(`CELL_PHONE`) USING BTREE,
  INDEX `IDX_IDCARDNO`(`ID_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_bank_card
-- ----------------------------
DROP TABLE IF EXISTS `t_user_bank_card`;
CREATE TABLE `t_user_bank_card`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `USER_ID` bigint(20) NOT NULL,
  `BANK_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_CREDIT_CARD` bit(1) NULL DEFAULT NULL,
  `IS_PRIMARY_CARD` bit(1) NOT NULL,
  `IS_DELETE` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_USER_ID_BANK_CARD_NO`(`USER_ID`, `BANK_CARD_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_login_report
-- ----------------------------
DROP TABLE IF EXISTS `t_user_login_report`;
CREATE TABLE `t_user_login_report`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '商户id',
  `MERCHANT_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商户名',
  `TOTAL_AMOUNT` bigint(20) NOT NULL DEFAULT 0 COMMENT '总数',
  `DISTINCT_AMOUNT` bigint(20) NOT NULL DEFAULT 0 COMMENT '根据用户去重总数',
  `REPORT_PERIOD` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '统计周期: daily, weekly, monthly, yearly',
  `REPORT_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'merchant, total',
  `START_DATE` date NOT NULL COMMENT '开始日期',
  `END_DATE` date NOT NULL COMMENT '截止日期',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE,
  INDEX `IDX_OPERATE_DATE`(`START_DATE`, `END_DATE`) USING BTREE,
  INDEX `IDX_REPORT_PERIOD`(`REPORT_PERIOD`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_operate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_user_operate_record`;
CREATE TABLE `t_user_operate_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `OPERATE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作类型',
  `DEVICE_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '设备类型',
  `USER_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
  `MERCHANT_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '商户id',
  `OPERATE_DATE` date NOT NULL COMMENT '操作日期',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE,
  INDEX `IDX_OPERATE_DATE`(`OPERATE_DATE`) USING BTREE,
  INDEX `IDX_OPERATE_TYPE`(`OPERATE_TYPE`) USING BTREE,
  INDEX `IDX_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_operate_report
-- ----------------------------
DROP TABLE IF EXISTS `t_user_operate_report`;
CREATE TABLE `t_user_operate_report`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '商户id',
  `CODE` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'XCXET2022001' COMMENT '活动代码',
  `FORMAT_DATE` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '格式化后的操作时间 yyyyMMdd',
  `USER_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
  `CUSTOMER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '网银客户号',
  `OPERATE_TYPE` int(11) NOT NULL DEFAULT 0 COMMENT '客户明细: 1、登陆2、考勤打卡3、工资条查询4、审批5、员工信息6、查看合同7、签署合同',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FORMAT_DATE`(`FORMAT_DATE`) USING BTREE,
  INDEX `IDX_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_password
-- ----------------------------
DROP TABLE IF EXISTS `t_user_password`;
CREATE TABLE `t_user_password`  (
  `PASSWORD_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `USER_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  `PASSWORD` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`PASSWORD_TYPE`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_password_type
-- ----------------------------
DROP TABLE IF EXISTS `t_user_password_type`;
CREATE TABLE `t_user_password_type`  (
  `NAME` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_MODIFY_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_voucher_apply
-- ----------------------------
DROP TABLE IF EXISTS `t_voucher_apply`;
CREATE TABLE `t_voucher_apply`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `USER_ID` bigint(20) NOT NULL,
  `START_DATE` date NOT NULL,
  `END_DATE` date NOT NULL,
  `STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `TARGET_STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_COMPREHENSIVE` bit(1) NOT NULL,
  `IS_TRADING` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `VOUCHER_A_IDX_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_voucher_apply_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_voucher_apply_detail`;
CREATE TABLE `t_voucher_apply_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `VOUCHER_APPLY_ID` bigint(20) NOT NULL,
  `INCOME_ID` bigint(20) NOT NULL,
  `VOUCHER_CREATE_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `VOUCHER_A_D_IDX_INCOME_ID`(`INCOME_ID`) USING BTREE,
  INDEX `VOUCHER_A_D_IDX_VOUCHER_APPLY_ID`(`VOUCHER_APPLY_ID`) USING BTREE,
  INDEX `VOUCHER_A_D_IDX_VOUCHER_CREATE_ID`(`VOUCHER_CREATE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_voucher_create
-- ----------------------------
DROP TABLE IF EXISTS `t_voucher_create`;
CREATE TABLE `t_voucher_create`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `VOUCHER_APPLY_ID` bigint(20) NOT NULL,
  `REQUEST_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ACCEPT_TIME` datetime NULL DEFAULT NULL,
  `GENERATE_TIME` datetime NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `TARGET_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `REQUEST_NO`(`REQUEST_NO`) USING BTREE,
  INDEX `VOUCHER_C_IDX_VOUCHER_APPLY_ID`(`VOUCHER_APPLY_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
