package main

import (
	"bufio"
	"fmt"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"time"
)

const (
	numEnterprises      = 1000
	minEmployeesPerCorp = 100
	maxEmployeesPerCorp = 150
	avgDeptsPerCorp     = 5
	avgPostsPerCorp     = 10
	avgRolesPerCorp     = 5
	outputSQLFile       = "merchant_data.sql"
)

var (
	userIDCounter           int64 = 0
	merchantIDCounter       int64 = 0
	merchantMemberIDCounter int64 = 0
	departmentIDCounter     int64 = 0
	deptMemberIDCounter     int64 = 0
	postIDCounter           int64 = 0
	postMemberIDCounter     int64 = 0
	roleIDCounter           int64 = 0
	roleMemberIDCounter     int64 = 0
	personalIDCounter       int64 = 0
	legalIDCounter          int64 = 0
	// ... 其他需要的计数器

	sqlWriter *bufio.Writer
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// --- Helper Functions ---
func randomInt(min, max int) int {
	if min >= max {
		return min
	}
	return rand.Intn(max-min+1) + min
}

func randomString(length int) string {
	bytes := make([]byte, length)
	for i := range bytes {
		bytes[i] = byte(rand.Intn(26) + 'a') // 小写字母
	}
	return string(bytes)
}

func randomName(prefix string, id int64) string {
	return fmt.Sprintf("%s-%d", prefix, id)
}

func randomCode(prefix string, id int64) string {
	return fmt.Sprintf("%sCODE%d", strings.ToUpper(prefix), id)
}

func randomPhone() string {
	return fmt.Sprintf("1%d%d%d%d%d%d%d%d%d", rand.Intn(7)+3, rand.Intn(10), rand.Intn(10), rand.Intn(10), rand.Intn(10), rand.Intn(10), rand.Intn(10), rand.Intn(10), rand.Intn(10))
}

func randomEmail(prefix string, id int64) string {
	return fmt.Sprintf("%<EMAIL>", strings.ToLower(prefix), id)
}

func randomDate(startYear, endYear int) string {
	year := randomInt(startYear, endYear)
	month := randomInt(1, 12)
	day := randomInt(1, 28) // 简化日期生成
	return fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", year, month, day, rand.Intn(24), rand.Intn(60), rand.Intn(60))
}

func currentTimeStamp() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func escapeSQL(s string) string {
	return strings.ReplaceAll(s, "'", "''")
}

func nullOrString(s string) string {
	if s == "" {
		return "NULL"
	}
	return fmt.Sprintf("'%s'", escapeSQL(s))
}

func nullOrInt(val int64, isNull bool) string {
	if isNull {
		return "NULL"
	}
	return strconv.FormatInt(val, 10)
}

func nullOrDecimal(val float64, isNull bool) string {
	if isNull {
		return "NULL"
	}
	return fmt.Sprintf("%.2f", val) // 假设保留两位小数
}

// --- SQL Generation Functions ---

func writeSQL(query string) {
	_, err := sqlWriter.WriteString(query + ";\n")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing SQL to file: %v\n", err)
	}
}

// --- Data Generation Logic ---

func generateMerchantData(merchantID int64) {
	// t_merchant
	merchantName := randomName("企业", merchantID)
	merchantCode := randomCode("merchant", merchantID)
	adminUserID := userIDCounter + 1 // 假设每个企业的第一个员工是管理员
	// ... 其他 t_merchant 字段
	insertMerchant := fmt.Sprintf("INSERT INTO t_merchant (ID, NAME, CODE, CREATE_TIME, MODIFY_TIME, IS_FORBIDDEN, ADMIN_USER_ID, DEFAULT_LEGAL_ID, STAGE) VALUES (%d, %s, %s, '%s', '%s', 0, %d, %d, 'APPROVED');",
		merchantID,
		nullOrString(merchantName),
		nullOrString(merchantCode),
		currentTimeStamp(),
		currentTimeStamp(),
		adminUserID,      // 稍后创建 user 时生成
		legalIDCounter+1, // 稍后创建 legal 时生成
	)
	writeSQL(insertMerchant)

	// t_legal (每个企业至少一个法人实体)
	legalIDCounter++
	legalName := randomName("法人", legalIDCounter)
	insertLegal := fmt.Sprintf("INSERT INTO t_legal (ID, MERCHANT_ID, NAME, CREATE_TIME, MODIFY_TIME, VERSION, SOCIAL_CREDIT_CODE, TYPE, IS_AUTH) VALUES (%d, %d, %s, '%s', '%s', 1, %s, 1, 1);",
		legalIDCounter,
		merchantID,
		nullOrString(legalName),
		currentTimeStamp(),
		currentTimeStamp(),
		nullOrString("CREDITCODE"+strconv.FormatInt(legalIDCounter, 10)),
	)
	writeSQL(insertLegal)

	// t_department (为每个企业生成一些部门)
	numDepts := randomInt(1, avgDeptsPerCorp*2)
	var rootDeptID int64 = 0
	deptHierarchy := make(map[int64]string) // To store ID_LINK and NAME_LINK

	for i := 0; i < numDepts; i++ {
		departmentIDCounter++
		deptName := randomName("部门", departmentIDCounter)
		var parentID int64 = 0
		var idLink, nameLink string

		if i == 0 { // 第一个部门作为根部门
			rootDeptID = departmentIDCounter
			parentID = 0 // 或者根据业务逻辑设为自身ID或特定值
			idLink = strconv.FormatInt(departmentIDCounter, 10)
			nameLink = deptName
		} else {
			// 随机选择一个已创建的部门作为父部门 (简化处理，实际可能需要更复杂的层级逻辑)
			// 为了简单，这里让非根部门都挂在根部门下
			if rootDeptID != 0 {
				parentID = rootDeptID
				idLink = deptHierarchy[rootDeptID] + "/" + strconv.FormatInt(departmentIDCounter, 10)
				nameLink = deptHierarchy[rootDeptID+"_name"] + "/" + deptName
			} else { // Fallback if rootDeptID was not set (should not happen with i==0 logic)
				parentID = 0
				idLink = strconv.FormatInt(departmentIDCounter, 10)
				nameLink = deptName
			}
		}
		deptHierarchy[departmentIDCounter] = idLink
		deptHierarchy[departmentIDCounter+"_name"] = nameLink

		insertDepartment := fmt.Sprintf("INSERT INTO t_department (ID, MERCHANT_ID, NAME, SEQUENCE, PARENT_ID, CREATE_TIME, LAST_UPDATE_TIME, VERSION, ID_LINK, NAME_LINK, PEOPLE_NUMBER) VALUES (%d, %d, %s, %d, %d, '%s', '%s', 1, %s, %s, 0);",
			departmentIDCounter,
			merchantID,
			nullOrString(deptName),
			i+1,
			parentID,
			currentTimeStamp(),
			currentTimeStamp(),
			nullOrString(idLink),
			nullOrString(nameLink),
		)
		writeSQL(insertDepartment)
	}

	// t_post (为每个企业生成一些岗位)
	numPosts := randomInt(1, avgPostsPerCorp*2)
	postIDs := []int64{}
	for i := 0; i < numPosts; i++ {
		postIDCounter++
		postName := randomName("岗位", postIDCounter)
		insertPost := fmt.Sprintf("INSERT INTO t_post (ID, MERCHANT_ID, NAME, CREATE_TIME, LAST_UPDATE_TIME, VERSION) VALUES (%d, %d, %s, '%s', '%s', 1);",
			postIDCounter,
			merchantID,
			nullOrString(postName),
			currentTimeStamp(),
			currentTimeStamp(),
		)
		writeSQL(insertPost)
		postIDs = append(postIDs, postIDCounter)
	}

	// t_role (为每个企业生成一些角色)
	numRoles := randomInt(1, avgRolesPerCorp*2)
	roleIDs := []int64{}
	for i := 0; i < numRoles; i++ {
		roleIDCounter++
		roleName := randomName("角色", roleIDCounter)
		roleCode := randomCode("role", roleIDCounter)
		insertRole := fmt.Sprintf("INSERT INTO t_role (ID, ROLE_NAME, ROLE_CODE, MERCHANT_ID, ROLE_TYPE, IS_BELONG_ADMIN, IS_DELETE, CREATE_TIME, LAST_UPDATE_TIME, VERSION, IS_ENABLED) VALUES (%d, %s, %s, %d, 'CUSTOM', 0, 0, '%s', '%s', 1, 1);",
			roleIDCounter,
			nullOrString(roleName),
			nullOrString(roleCode),
			merchantID,
			currentTimeStamp(),
			currentTimeStamp(),
		)
		writeSQL(insertRole)
		roleIDs = append(roleIDs, roleIDCounter)
	}

	// 为该企业生成员工
	numEmployees := randomInt(minEmployeesPerCorp, maxEmployeesPerCorp)
	for j := 0; j < numEmployees; j++ {
		userIDCounter++
		currentUserID := userIDCounter

		// t_user
		userName := randomName("用户", currentUserID)
		userCellPhone := randomPhone()
		userEmail := randomEmail("user", currentUserID)
		insertUser := fmt.Sprintf("INSERT INTO t_user (ID, CREATE_TIME, MODIFY_TIME, VERSION, CELLPHONE, EMAIL, NAME, IS_AUTH, IS_ENABLE, DEFAULT_MERCHANT_ID) VALUES (%d, '%s', '%s', 1, %s, %s, %s, 1, 1, %d);",
			currentUserID,
			currentTimeStamp(),
			currentTimeStamp(),
			nullOrString(userCellPhone),
			nullOrString(userEmail),
			nullOrString(userName),
			merchantID,
		)
		writeSQL(insertUser)

		// t_personal
		personalIDCounter++
		// OWNER_TYPE for user might be a specific integer, let's assume 1 for USER
		insertPersonal := fmt.Sprintf("INSERT INTO t_personal (ID, CREATE_TIME, MODIFY_TIME, VERSION, OWNER_ID_LONG, OWNER_TYPE, NAME, CELLPHONE, GENDER) VALUES (%d, '%s', '%s', 1, %d, 1, %s, %s, %d);",
			personalIDCounter,
			currentTimeStamp(),
			currentTimeStamp(),
			currentUserID,
			nullOrString(userName),      // Re-use user name for simplicity
			nullOrString(userCellPhone), // Re-use user phone
			randomInt(0, 1),             // Assuming 0 for male, 1 for female
		)
		writeSQL(insertPersonal)

		// t_merchant_member
		merchantMemberIDCounter++
		insertMerchantMember := fmt.Sprintf("INSERT INTO t_merchant_member (ID, CREATE_TIME, MODIFY_TIME, VERSION, MERCHANT_ID, USER_ID, NAME, CELL_PHONE, EMAIL, HAVE_ENABLED, IS_DELETE, TENANT_ID, PERSONAL_ID) VALUES (%d, '%s', '%s', 1, %d, %d, %s, %s, %s, 1, 0, %d, %d);",
			merchantMemberIDCounter,
			currentTimeStamp(),
			currentTimeStamp(),
			merchantID,
			currentUserID,
			nullOrString(userName),
			nullOrString(userCellPhone),
			nullOrString(userEmail),
			merchantID, // Assuming tenant_id is same as merchant_id for simplicity
			personalIDCounter,
		)
		writeSQL(insertMerchantMember)

		// t_dept_member (分配到随机一个部门)
		if numDepts > 0 {
			deptMemberIDCounter++
			// Assign to a random department of this merchant
			// For simplicity, let's pick the root department if it exists, or the first one.
			targetDeptID := rootDeptID
			if targetDeptID == 0 && departmentIDCounter >= (merchantID-1)*avgDeptsPerCorp+1 { // ensure dept exists
				targetDeptID = (merchantID-1)*avgDeptsPerCorp + 1 // first dept of this merchant
			}
			if targetDeptID != 0 { // only insert if a department was actually created
				insertDeptMember := fmt.Sprintf("INSERT INTO t_dept_member (ID, CREATE_TIME, MODIFY_TIME, VERSION, DEPT_ID, USER_ID, MERCHANT_ID, MERCHANT_MEMBER_ID, NAME) VALUES (%d, '%s', '%s', 1, %d, %d, %d, %d, %s);",
					deptMemberIDCounter,
					currentTimeStamp(),
					currentTimeStamp(),
					targetDeptID,
					currentUserID,
					merchantID,
					merchantMemberIDCounter,
					nullOrString(userName),
				)
				writeSQL(insertDeptMember)
			}
		}

		// t_post_member (分配到随机一个岗位)
		if len(postIDs) > 0 {
			postMemberIDCounter++
			targetPostID := postIDs[rand.Intn(len(postIDs))]
			insertPostMember := fmt.Sprintf("INSERT INTO t_post_member (ID, MERCHANT_ID, POST_ID, USER_ID, CREATE_TIME, MODIFY_TIME, VERSION) VALUES (%d, %d, %d, %d, '%s', '%s', 1);",
				postMemberIDCounter,
				merchantID,
				targetPostID,
				currentUserID,
				currentTimeStamp(),
				currentTimeStamp(),
			)
			writeSQL(insertPostMember)
		}

		// t_role_member (分配到随机一个角色)
		if len(roleIDs) > 0 {
			roleMemberIDCounter++
			targetRoleID := roleIDs[rand.Intn(len(roleIDs))]
			insertRoleMember := fmt.Sprintf("INSERT INTO t_role_member (ID, CREATE_TIME, MODIFY_TIME, VERSION, MERCHANT_ID, ROLE_ID, USER_ID) VALUES (%d, '%s', '%s', 1, %d, %d, %d);",
				roleMemberIDCounter,
				currentTimeStamp(),
				currentTimeStamp(),
				merchantID,
				targetRoleID,
				currentUserID,
			)
			writeSQL(insertRoleMember)
		}
	}
}

func main() {
	file, err := os.Create(outputSQLFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating SQL file: %v\n", err)
		return
	}
	defer file.Close()

	sqlWriter = bufio.NewWriter(file)
	defer sqlWriter.Flush()

	fmt.Fprintf(sqlWriter, "SET NAMES utf8mb4;\n")
	fmt.Fprintf(sqlWriter, "SET FOREIGN_KEY_CHECKS = 0;\n\n")

	for i := 0; i < numEnterprises; i++ {
		merchantIDCounter++
		fmt.Printf("Generating data for Merchant ID: %d\n", merchantIDCounter)
		generateMerchantData(merchantIDCounter)
		if (i+1)%10 == 0 { // Flush every 10 enterprises to manage memory
			sqlWriter.Flush()
		}
	}

	fmt.Fprintf(sqlWriter, "\nSET FOREIGN_KEY_CHECKS = 1;\n")
	fmt.Printf("Successfully generated data for %d enterprises into %s\n", numEnterprises, outputSQLFile)
}
