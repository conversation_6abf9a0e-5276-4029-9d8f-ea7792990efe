/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : basic

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:24:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_area_import_record
-- ----------------------------
DROP TABLE IF EXISTS `t_area_import_record`;
CREATE TABLE `t_area_import_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `CREATE_TIME` datetime NOT NULL,
  `FILE_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_area_info
-- ----------------------------
DROP TABLE IF EXISTS `t_area_info`;
CREATE TABLE `t_area_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AREA_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `AREA_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `IMPORT_RECORD_ID` bigint(20) NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `LEVEL` int(11) NOT NULL,
  `PARENT_AREA_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `VALID_END_DATE` datetime NULL DEFAULT NULL,
  `VALID_START_DATE` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `AREA_INFO_UK_AREA_CODE_VALID_START_DATE`(`AREA_CODE`, `VALID_START_DATE`) USING BTREE,
  INDEX `AREA_INFO_IDX_AREA_CODE`(`AREA_CODE`) USING BTREE,
  INDEX `AREA_INFO_IDX_AREA_NAME`(`AREA_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_auth_cache
-- ----------------------------
DROP TABLE IF EXISTS `t_auth_cache`;
CREATE TABLE `t_auth_cache`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `AUTH_MODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '鉴权类型',
  `BANKCARD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行卡号',
  `CELLPHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `IS_OK` bit(1) NULL DEFAULT NULL COMMENT '是否通过验证',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_AUTH_CACHE_5_FIELD`(`NAME`, `CELLPHONE`, `ID_CARD_NO`, `BANKCARD`, `AUTH_MODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_auth_history
-- ----------------------------
DROP TABLE IF EXISTS `t_auth_history`;
CREATE TABLE `t_auth_history`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `ACTION_RATE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACTION_SIMILARITY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `DELIVERY_CODE` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_MSG` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORDER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FLOW_NO` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FILE_SIZE` bigint(20) NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `AUTH_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTH_CHANNEL` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ORDER_NO`(`ORDER_NO`) USING BTREE,
  INDEX `IDX_AUTH_FLOW_NO_TYPE`(`TENANT_ID`, `FLOW_NO`, `TYPE`) USING BTREE,
  INDEX `I_AUTH_CREATE_TIME`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_branch_info
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_branch_info`;
CREATE TABLE `t_bank_branch_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ADDRESS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_SWIFT_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BRANCH_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BRANCH_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CNAPS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `PHONE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `BANK_BRANCH_INFO_IDX_BRANCH_CODE`(`BRANCH_CODE`) USING BTREE,
  INDEX `BANK_BRANCH_INFO_IDX_BRANCH_NAME`(`BRANCH_NAME`) USING BTREE,
  INDEX `BANK_BRANCH_INFO_IDX_CNAPS_CODE`(`CNAPS_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info`;
CREATE TABLE `t_bank_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `BANK_FULL_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BANK_SIMPLE_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_SWIFT_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `LEGAL_FULL_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `BANK_INFO_IDX_BANK_FULL_NAME`(`BANK_FULL_NAME`) USING BTREE,
  INDEX `BANK_INFO_IDX_BANK_SWIFT_CODE`(`BANK_SWIFT_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_certification_issuer_record
-- ----------------------------
DROP TABLE IF EXISTS `t_certification_issuer_record`;
CREATE TABLE `t_certification_issuer_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  `IDENTIFICATION_NUMBER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `ISSUE_SERIAL_NO` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书颁发序列号',
  `ISSUER` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书颁发者',
  `PRIVATE_KEY_SHA256` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '私钥哈希',
  `SUBJECT_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '迁移主体',
  `SUBJECT_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主体类型',
  `TENANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  `X509_CERTIFICATES` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'X509证书',
  `X509_SHA256` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'X509证书哈希',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_external_service_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_external_service_detail`;
CREATE TABLE `t_external_service_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `FLOW_NO` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `SERVICE_PROVIDER_ID` bigint(20) NULL DEFAULT NULL,
  `PLATE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_MSG` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REQ_MESSAGE` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `RSP_MESSAGE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_internal_service_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_internal_service_detail`;
CREATE TABLE `t_internal_service_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `FLOW_NO` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `SERVICE_PROVIDER_ID` bigint(20) NULL DEFAULT NULL,
  `PLATE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_MSG` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REQ_MESSAGE` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `RSP_MESSAGE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_mail_account
-- ----------------------------
DROP TABLE IF EXISTS `t_mail_account`;
CREATE TABLE `t_mail_account`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `DESCRIBE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROTOCOL` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PORT` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MAIL_HOST` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MAIL_ADDRESS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MAIL_NICKNAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MAIL_PASSWORD` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `IS_AUTH` bit(1) NOT NULL,
  `IS_ENABLED_DEBUG_MOD` bit(1) NOT NULL,
  `IS_ENABLED_SSL` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CODE`(`CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_mail_template
-- ----------------------------
DROP TABLE IF EXISTS `t_mail_template`;
CREATE TABLE `t_mail_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `NAME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CODE`(`CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_notice_send_record
-- ----------------------------
DROP TABLE IF EXISTS `t_notice_send_record`;
CREATE TABLE `t_notice_send_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `USER_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `REQUEST_PARAMS` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_resume_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_resume_request_detail`;
CREATE TABLE `t_resume_request_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CHANNEL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '使用通道',
  `FILE_MD5` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '文件MD5值',
  `FILE_NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '文件名',
  `FILE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `STATUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '通道请求状态',
  `ORIGIN_MESSAGE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '通道请求信息(一般为错误信息--原始信息)',
  `MESSAGE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '标准信息(一般为错误信息)',
  `RESULT` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '三方原始结果',
  `CREATE_TIME` datetime NOT NULL COMMENT '请求时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `FILE_MD5`(`FILE_MD5`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '简历解析请求详情' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_resume_request_record
-- ----------------------------
DROP TABLE IF EXISTS `t_resume_request_record`;
CREATE TABLE `t_resume_request_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TENANT_ID` bigint(20) NOT NULL COMMENT '商户ID',
  `DEVELOPER_ID` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开发者账号',
  `CHANNEL_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '使用通道',
  `DETAIL_ID` bigint(20) NOT NULL COMMENT '请求记录ID',
  `STATUS` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求总体状态',
  `MESSAGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '请求信息(一般为错误信息)',
  `CREATE_TIME` datetime NOT NULL COMMENT '请求时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '简历解析请求记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_send_mail_record
-- ----------------------------
DROP TABLE IF EXISTS `t_send_mail_record`;
CREATE TABLE `t_send_mail_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MAIL_CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `TEMPLATE_CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PARAMETER` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RESULT` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ERROR_INFO` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `SEND_MAIL_RECORD_IDX_TO`(`TO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_service_provider
-- ----------------------------
DROP TABLE IF EXISTS `t_service_provider`;
CREATE TABLE `t_service_provider`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `INTERFACE_SERVICE_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SERVICE_PROVIDER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_business
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_business`;
CREATE TABLE `t_sms_business`  (
  `ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `CHANNEL_ID` bigint(20) NULL DEFAULT NULL,
  `DESCRIPTION` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `SIGN` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TEMPLATE_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_channel
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_channel`;
CREATE TABLE `t_sms_channel`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PRIVATE_KEY` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROVIDER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_channel_request
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_channel_request`;
CREATE TABLE `t_sms_channel_request`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `BUSINESS_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHANNEL_ID` bigint(20) NULL DEFAULT NULL,
  `CONTENT` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGN` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TEMPLATE_ID` bigint(20) NULL DEFAULT NULL,
  `MESSAGE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` int(11) NULL DEFAULT NULL,
  `RECEIVER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '短信接收人电话',
  `DEVELOPER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_SCR_CTIME`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_config
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_config`;
CREATE TABLE `t_sms_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SYSTEM_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `SYSTEM_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SMS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRIVATE_KEY` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `SYSTEM_CODE`(`SYSTEM_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_lanmaoly_config
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_lanmaoly_config`;
CREATE TABLE `t_sms_lanmaoly_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `BIZ_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRIVATE_KEY` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SYSTEM_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_send_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_send_record`;
CREATE TABLE `t_sms_send_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MOBILE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `SYSTEM_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `REQUEST_PARAMS` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `INDEX_SMS_SEND_RECORD`(`MOBILE`, `SYSTEM_CODE`, `BUSINESS_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sms_template
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_template`;
CREATE TABLE `t_sms_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `DESCRIPTION` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `TEMPLATE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOCKED_TIME` datetime NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE,
  INDEX `I_SYS_MQ_LOG_TIME`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_work_calendar
-- ----------------------------
DROP TABLE IF EXISTS `t_work_calendar`;
CREATE TABLE `t_work_calendar`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CALENDAR` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `YEAR` int(11) NOT NULL,
  `IS_HOLIDAY` bit(1) NULL DEFAULT NULL,
  `DATE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `WAGE` decimal(4, 2) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `MODIFY_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
