/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : xst_salary

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:38:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `BLOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `SCHED_NAME`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CALENDAR_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CALENDAR` blob NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CRON_EXPRESSION` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TIME_ZONE_ID` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ENTRY_ID` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `FIRED_TIME` bigint(13) NOT NULL,
  `SCHED_TIME` bigint(13) NOT NULL,
  `PRIORITY` int(11) NOT NULL,
  `STATE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `ENTRY_ID`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TRIG_INST_NAME`(`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY`(`SCHED_NAME`, `INSTANCE_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_FT_J_G`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_T_G`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TG`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `JOB_CLASS_NAME` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_DURABLE` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_UPDATE_DATA` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_J_REQ_RECOVERY`(`SCHED_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_J_GRP`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `LOCK_NAME` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `LOCK_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `LAST_CHECKIN_TIME` bigint(13) NOT NULL,
  `CHECKIN_INTERVAL` bigint(13) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `REPEAT_COUNT` bigint(7) NOT NULL,
  `REPEAT_INTERVAL` bigint(12) NOT NULL,
  `TIMES_TRIGGERED` bigint(10) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `STR_PROP_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `STR_PROP_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `STR_PROP_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `INT_PROP_1` int(11) NULL DEFAULT NULL,
  `INT_PROP_2` int(11) NULL DEFAULT NULL,
  `LONG_PROP_1` bigint(20) NULL DEFAULT NULL,
  `LONG_PROP_2` bigint(20) NULL DEFAULT NULL,
  `DEC_PROP_1` decimal(13, 4) NULL DEFAULT NULL,
  `DEC_PROP_2` decimal(13, 4) NULL DEFAULT NULL,
  `BOOL_PROP_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `BOOL_PROP_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `NEXT_FIRE_TIME` bigint(13) NULL DEFAULT NULL,
  `PREV_FIRE_TIME` bigint(13) NULL DEFAULT NULL,
  `PRIORITY` int(11) NULL DEFAULT NULL,
  `TRIGGER_STATE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_TYPE` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `START_TIME` bigint(13) NOT NULL,
  `END_TIME` bigint(13) NULL DEFAULT NULL,
  `CALENDAR_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `MISFIRE_INSTR` smallint(2) NULL DEFAULT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_J`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_C`(`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_T_G`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_STATE`(`SCHED_NAME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_STATE`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_G_STATE`(`SCHED_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NEXT_FIRE_TIME`(`SCHED_NAME`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST`(`SCHED_NAME`, `TRIGGER_STATE`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_adjust_salary_config
-- ----------------------------
DROP TABLE IF EXISTS `t_adjust_salary_config`;
CREATE TABLE `t_adjust_salary_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENABLE_EDIT` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQUENCE` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_ADJUST_COMP_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_data
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_data`;
CREATE TABLE `t_apportion_data`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NULL DEFAULT NULL,
  `PLAN_ID` int(11) NULL DEFAULT NULL,
  `EMP_ID` int(11) NULL DEFAULT NULL,
  `EMP_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '员工姓名',
  `EMP_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工号',
  `MOBILE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `ID_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `SALARY_MONTH` date NULL DEFAULT NULL COMMENT '税款所属期',
  `TAX_SUB_ID` int(11) NULL DEFAULT NULL COMMENT '所属公司',
  `TAX_SUB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称',
  `DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '部门id',
  `DEPT_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
  `COST_CENTER` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '成本中心',
  `PROJECT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目',
  `CONTRACT_SUB_ID` int(11) NULL DEFAULT NULL COMMENT '合同主体id',
  `RATIO` decimal(8, 2) NULL DEFAULT NULL COMMENT '分摊比例(%)',
  `DATE` date NULL DEFAULT NULL COMMENT '工时日期',
  `WORKING_HOURS` decimal(8, 2) NULL DEFAULT NULL COMMENT '工时',
  `WORKING_DAY` decimal(8, 2) NULL DEFAULT NULL COMMENT '工时(天)',
  `WORKING_MINUTE` decimal(8, 2) NULL DEFAULT NULL COMMENT '工时（分钟）',
  `OPERATOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊数据维护表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_plan`;
CREATE TABLE `t_apportion_plan`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `PLAN_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方案名称',
  `DATA_SOURCE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据来源',
  `APPORTION_TYPE` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分摊类型',
  `DEPT_SOURCE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门数据来源',
  `COST_SOURCE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '成本中心数据来源',
  `CONTRACT_SOURCE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同公司数据来源',
  `PROJECT_SOURCE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目数据来源',
  `APPORTION_METHOD` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分摊方式',
  `DIRECTORS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '负责人',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊方案表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_plan_range
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_plan_range`;
CREATE TABLE `t_apportion_plan_range`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `PLAN_ID` int(11) NOT NULL COMMENT '分摊计划id',
  `EMP_RANGE_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员范围类型',
  `UNIQUE_IDS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '唯一id列表',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊方案分摊范围表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_record
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_record`;
CREATE TABLE `t_apportion_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NULL DEFAULT NULL COMMENT '企业id',
  `PLAN_ID` int(11) NULL DEFAULT NULL COMMENT '分摊计划id',
  `COUNT_DATE_RANGE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '统计日期起',
  `APPORTION_TYPE` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分摊维度类型',
  `OPERATOR` int(11) NULL DEFAULT NULL COMMENT '操作人id',
  `OPERATOR_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人姓名',
  `GENERATE_TIME` timestamp NULL DEFAULT NULL COMMENT '生成时间',
  `SALARY_RULE_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资表id',
  `SALARY_ITEM` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资项',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊结果记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_result
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_result`;
CREATE TABLE `t_apportion_result`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业id',
  `COUNT_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '分摊记录id',
  `TAX_SUB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `DEPT_NAME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
  `COST_CENTER` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '成本中心',
  `CONTRACT_SUB_ID` bigint(20) NULL DEFAULT NULL COMMENT '合同主体id',
  `PROJECT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目',
  `EMP_SUM` int(11) NULL DEFAULT NULL COMMENT '总人数',
  `SALARY_ITEM_SUM` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '薪资项汇总结果',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊结果汇总表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_apportion_result_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_apportion_result_detail`;
CREATE TABLE `t_apportion_result_detail`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业id',
  `COUNT_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '分摊记录id',
  `TAX_SUB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `DEPT_NAME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
  `COST_CENTER` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '成本中心',
  `PROJECT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目',
  `CONTRACT_SUB_ID` bigint(20) NULL DEFAULT NULL COMMENT '合同主体id',
  `EMP_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '员工姓名',
  `EMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '员工Id',
  `ID_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `SALARY_ITEM_RESULT` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '薪资项统计结果',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分摊结果详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_area
-- ----------------------------
DROP TABLE IF EXISTS `t_area`;
CREATE TABLE `t_area`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `AREA_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ISENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  `CERATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_areas
-- ----------------------------
DROP TABLE IF EXISTS `t_areas`;
CREATE TABLE `t_areas`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `area_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `parent_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `level` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_update_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_area_code`(`area_code`) USING BTREE,
  INDEX `idx_parent_code`(`parent_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_async_import_task
-- ----------------------------
DROP TABLE IF EXISTS `t_async_import_task`;
CREATE TABLE `t_async_import_task`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `FILE_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件key',
  `FILE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件名',
  `TASK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务状态',
  `IMPORT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '导入类型',
  `MATCHING_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '导入时匹配方式',
  `START_TIME` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `END_TIME` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `SUCCESS_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '成功条数',
  `FAILED_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '失败条数',
  `TOTAL_NUMBER` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务总条数',
  `TASK_OWNER` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务所有人',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FIELD_CODES` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '算薪记录id',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ASY_COMP_CHECK`(`COMP_ID`, `CHECK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_async_import_task_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_async_import_task_detail`;
CREATE TABLE `t_async_import_task_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TASK_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务id',
  `IMPORT_DATA` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `COL` int(11) NULL DEFAULT NULL COMMENT '错误列',
  `ERROR_MSG` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `TITLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否为表头',
  `ROW` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ASY_TASK_ID`(`TASK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attendance_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_attendance_detail`;
CREATE TABLE `t_attendance_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `SALARY_CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '工资表id',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工id',
  `SALARY_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '算薪人员表id',
  `WORK_DATE` date NULL DEFAULT NULL COMMENT '日期',
  `ATTEND_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '考勤组id',
  `MUST_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '应出勤时长(分钟)',
  `REAL_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '实际出勤时长(分钟)',
  `BALANCE_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '计薪工作时长(分钟)',
  `LATE_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '迟到时长(分钟)',
  `LEAVE_EARLY_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '早退时长(分钟)',
  `LATE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '迟到次数',
  `LEAVE_EARLY_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '早退次数',
  `BEGIN_WORK_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '上班缺卡次数',
  `END_WORK_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '下班缺卡次数',
  `BUSINESS_TRIP_TIME_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '出差时长(分钟)',
  `OUTSIDE_ATTEND_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '外出时长(分钟)',
  `CHANGE_WORK_TIME_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '换班天数',
  `CHANGE_WORK_SHIFT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '换班班次',
  `EXT_HOLIDAY` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '假期拓展字段',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_DETAIL_SALARY_EMP_ID`(`COMP_ID`, `SALARY_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤核算表明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attendance_record
-- ----------------------------
DROP TABLE IF EXISTS `t_attendance_record`;
CREATE TABLE `t_attendance_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `SALARY_CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '工资表id',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工id',
  `SALARY_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '算薪人员表id',
  `DEPT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '部门id',
  `ATTEND_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '考勤组id',
  `AG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '考勤组名称',
  `TIME_ZONE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '时区',
  `MUST_TURN_OUT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '应出勤天数',
  `WORK_DAY_OUT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '工作日出勤天数',
  `REST_DAY_OUT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '休息日出勤天数',
  `MUST_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '应出勤时长(分钟)',
  `REAL_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '实际出勤时长(分钟)',
  `BALANCE_WORK_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '计薪工作时长(分钟)',
  `OVER_TIME_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '加班工作时长(分钟)',
  `LATE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '迟到次数',
  `LATE_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '迟到时长(分钟)',
  `LEAVE_EARLY_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '早退次数',
  `LEAVE_EARLY_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '早退时长(分钟)',
  `BEGIN_WORK_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '上班缺卡次数',
  `END_WORK_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '下班缺卡次数',
  `ABSENT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '缺勤次数',
  `BUSINESS_TRIP_TIME_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '出差时长',
  `SUPPLEMENT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '补卡次数',
  `OUTSIDE_ATTEND_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '外勤次数',
  `OUTSIDE_ATTEND_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '外出时长(分钟)',
  `CHANGE_WORK_TIME_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '换班天数',
  `WORK_DAY_OVER_TIME_TO_BALANCE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '工作日转加班费',
  `REST_DAY_OVER_TIME_TO_BALANCE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '休息日转加班费',
  `LEAVE_DAY_OVER_TIME_TO_BALANCE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '节假日转加班费',
  `WORK_DAY_OVER_TIME_TO_LEAVE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '工作日转调休',
  `REST_DAY_OVER_TIME_TO_LEAVE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '休息日转调休',
  `LEAVE_DAY_OVER_TIME_TO_LEAVE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '节假日转调休',
  `EXT_HOLIDAY` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '假期拓展字段',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_SALARY_CHECK_EMP_ID`(`SALARY_CHECK_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤核算汇总表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attendance_total
-- ----------------------------
DROP TABLE IF EXISTS `t_attendance_total`;
CREATE TABLE `t_attendance_total`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ARCHIVE_STATUS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ATTENDANCE_MONTH` date NULL DEFAULT NULL,
  `ATTR_DATA` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_base_insured_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_base_insured_detail`;
CREATE TABLE `t_base_insured_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `BASE_INSURED_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `INSURED_PROJECT_TYPE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INSURANCE_TYPE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BASE_NUMBER_MAX` decimal(10, 2) NULL DEFAULT NULL,
  `BASE_NUMBER_MIN` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `PERSON_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_MANTISSA_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `COMP_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_MANTISSA_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_base_insured_project
-- ----------------------------
DROP TABLE IF EXISTS `t_base_insured_project`;
CREATE TABLE `t_base_insured_project`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `INSURED_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INSURED_CITY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCUMULATION_FUND_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_calendar
-- ----------------------------
DROP TABLE IF EXISTS `t_calendar`;
CREATE TABLE `t_calendar`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CALENDAR` date NULL DEFAULT NULL,
  `CALENDAR_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HOLIDAY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CALENDAR_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_common_field
-- ----------------------------
DROP TABLE IF EXISTS `t_common_field`;
CREATE TABLE `t_common_field`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段名',
  `RELATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '关联项',
  `RELATION_GROUP` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项所属分组',
  `RELATION_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项名称',
  `SIGNATORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '签署方(SEAL-企业签署方 SIGN-个人签署方)',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '合同签署系统预设字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_emp_insured
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_emp_insured`;
CREATE TABLE `t_comp_emp_insured`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_INSURED_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SOCIAL_INSURANCE_START_MONTH` date NULL DEFAULT NULL,
  `SOCIAL_INSURANCE_BASE_NUMBER` decimal(10, 2) NULL DEFAULT NULL,
  `ACCUMULATION_FUND_START_MONTH` date NULL DEFAULT NULL,
  `ACCUMULATION_FUND_BASE_NUMBER` decimal(10, 2) NULL DEFAULT NULL,
  `INSURED_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOCIAL_INSURANCE_END_MONTH` date NULL DEFAULT NULL,
  `ACCUMULATION_FUND_END_MONTH` date NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `END_INSURED_TIME` timestamp NULL DEFAULT NULL,
  `HR_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_emp_pwd
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_emp_pwd`;
CREATE TABLE `t_comp_emp_pwd`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PWD_ENCRYPT` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PWD_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_COMP_EMP_PWD_ID`(`COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_emp_user_record
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_emp_user_record`;
CREATE TABLE `t_comp_emp_user_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '企业id',
  `COMP_EMP_ID` bigint(20) NOT NULL COMMENT '企业人员id',
  `PLATFORM_USER_ID` bigint(20) NOT NULL COMMENT '平台用户ID，关联merchant.t_user.id',
  `EFFECTIVE` bit(1) NOT NULL COMMENT '有效状态：0（有效），1（无效）',
  `DELETED` bit(1) NOT NULL COMMENT '删除状态：0（未删除），1（已删除）',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_compId_compEmpId_userId`(`COMP_ID`, `COMP_EMP_ID`, `EFFECTIVE`, `DELETED`) USING BTREE,
  INDEX `idx_compId_userId_deleted`(`COMP_ID`, `PLATFORM_USER_ID`, `DELETED`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工-平台用户关联记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_insured_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_insured_detail`;
CREATE TABLE `t_comp_insured_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_INSURED_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `INSURED_PROJECT_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INSURANCE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BASE_NUMBER_MAX` decimal(10, 2) NULL DEFAULT NULL,
  `BASE_NUMBER_MIN` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `PERSON_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_MANTISSA_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `COMP_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_MANTISSA_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_insured_project
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_insured_project`;
CREATE TABLE `t_comp_insured_project`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_INSURED_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INSURED_CITY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BASE_INSURED_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ACCUMULATION_FUND_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_manager
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_manager`;
CREATE TABLE `t_comp_manager`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `MANAGER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OPEN_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TENANT_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_COMP_MANAGER_ID`(`COMP_ID`) USING BTREE,
  INDEX `I_COMP_MANAGER_TENANT`(`TENANT_KEY`, `OPEN_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_monthly_ledger_total
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_monthly_ledger_total`;
CREATE TABLE `t_comp_monthly_ledger_total`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_MONTH` date NULL DEFAULT NULL,
  `INSURED_PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_MONTH_ADD` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_MONTH_SUB` int(10) UNSIGNED NULL DEFAULT NULL,
  `PERSON_SOCIAL_SUM_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_SOCIAL_SUM_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_FUND_SUM_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_FUND_SUM_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `ISARCHIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ARCHIVE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_LEDGER_TOTAL_COMP`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_options
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_options`;
CREATE TABLE `t_comp_options`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NULL DEFAULT NULL COMMENT '企业id',
  `OPTION_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名',
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段编码',
  `RELATION_OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联字段编码（编码与模板字段的编码保持一致）',
  `SYSTEM_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否为系统预设项',
  `ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否启用',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_COMP_OPTIONS_COMP_ID_index`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_options_enum
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_options_enum`;
CREATE TABLE `t_comp_options_enum`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '企业选项字典编码',
  `OPTION_ENUM_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '枚举名称',
  `OPTION_ENUM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '枚举编码',
  `ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否启用',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排列序号',
  `RELATION_OPTION_ENUM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联枚举编码',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `EDIT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否可修改',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_COMP_OPTION_CODE`(`COMP_ID`, `OPTION_CODE`) USING BTREE,
  INDEX `T_COMP_OPTIONS_ENUM_COMP_ID_index`(`COMP_ID`) USING BTREE,
  INDEX `t_comp_options_enum_relation_option_enum_code_idx`(`RELATION_OPTION_ENUM_CODE`) USING BTREE,
  INDEX `t_comp_options_enum_seq_no_idx`(`SEQ_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_plan_order
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_plan_order`;
CREATE TABLE `t_comp_plan_order`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ORDER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRICE_PLAN_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRICE_PLAN_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `BUY_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `PAY_TIME` timestamp NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUY_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SRC_ORDER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DST_ORDER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORDER_PAY_PRICE` decimal(10, 2) NULL DEFAULT NULL,
  `TENANT_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ORDER_ID`(`ORDER_ID`, `SOURCE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_price_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_price_plan`;
CREATE TABLE `t_comp_price_plan`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TENANT_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUY_PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `REAL_PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `START_DAY` date NULL DEFAULT NULL,
  `DURATION_DAY` int(10) UNSIGNED NULL DEFAULT NULL,
  `DURATION_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `END_DAY` date NULL DEFAULT NULL,
  `REAL_END_DAY` date NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUY_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `PLAN_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REMAIN_PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_COMP_PLAN_TENANT`(`TENANT_KEY`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_template_field
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_template_field`;
CREATE TABLE `t_comp_template_field`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `FIELD_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名称',
  `FIELD_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段编码',
  `FIELD_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段类型',
  `GROUP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分组编码',
  `EMPLOYYE_DETAIL_PC` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '显示详情页（PC端）',
  `EMPLOYEE_DETAIL_H5` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个人信息界面（移动端）',
  `EMPLOYEE_ENTRY_PC` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表（PC端）',
  `EMPLOYEE_ENTRY_H5` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表（移动端）',
  `ADD_EMPLOYEE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '添加员工页',
  `SYSTEM_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否为系统预设',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排序序号',
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下拉选项',
  `FIELD_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '小数位数',
  `FIELD_REMARK` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提示文案',
  `TIPS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'tips文案',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否可删除',
  `LABLE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分类编码',
  `MUILTPLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否支持多选',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMP_COMP_GROUPCODE`(`COMP_ID`, `GROUP_CODE`) USING BTREE,
  INDEX `T_COMP_TEMPLATE_FIELD_COMP_ID_index`(`COMP_ID`) USING BTREE,
  INDEX `t_comp_template_field_group_code_idx`(`GROUP_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_comp_template_group
-- ----------------------------
DROP TABLE IF EXISTS `t_comp_template_group`;
CREATE TABLE `t_comp_template_group`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `GROUP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组名称',
  `GROUP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组编码',
  `LABLE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分组编码',
  `LABLE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分组名称',
  `ADD_MUILTPLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否支持多条录入',
  `SYSTEM_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否为系统预设',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否可删除',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排序序号',
  `EXPORT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否支持导出',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMPLATE_COMP_ID`(`COMP_ID`, `LABLE_CODE`) USING BTREE,
  INDEX `T_COMP_TEMPLATE_GROUP_COMP_ID_index`(`COMP_ID`) USING BTREE,
  INDEX `t_comp_template_group_lable_code_idx`(`LABLE_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_company
-- ----------------------------
DROP TABLE IF EXISTS `t_company`;
CREATE TABLE `t_company`  (
  `COMP_ID` int(10) UNSIGNED NOT NULL,
  `COMP_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `PAYROLL_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FEISHU_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOURCE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DATA_AUTH_ENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TEMPLATE_FIELD_COMMON` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENTRY_WHITE_LIST` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '入职白名单',
  `BATCH_OPEN_ACCOUNT_YN` int(11) NULL DEFAULT NULL,
  `ELECTRONNIC_CARD_YN` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_company_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_company_employee`;
CREATE TABLE `t_company_employee`  (
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_SEX` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTHDAY` date NULL DEFAULT NULL,
  `EMP_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IN_WORK_DAY` date NULL DEFAULT NULL,
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EDUCATION` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MARITAL_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HOUSEHOLD_REGISTRATION_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HOUSEHOLD_COUNTRY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATIONALITY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OTHER_ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OTHER_ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTH_PLACE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RELATED_REASON` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FIRST_ENTRY_TIME` date NULL DEFAULT NULL,
  `EXPECT_LEAVE_TIME` date NULL DEFAULT NULL,
  `ENGLISH_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `OPEN_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOURCE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `AUTHENTICATION_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NULL DEFAULT 0,
  `NATIVE_PLACE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PLATFORM_USER_ID` bigint(20) NULL DEFAULT NULL,
  `LUNAR_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTHDAY_DATE` date NULL DEFAULT NULL,
  `REGISTERED_RESIDENCE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHILDREN_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `POLITICAL_ORIENTATION` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ADJUST_WORKING_YEARS` int(11) NULL DEFAULT NULL,
  `EDIT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `CUSTOM_FIELD` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DING_USER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '钉钉用户id',
  `PLATFORM_DELETED_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `PLATFORM_DISABLED_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `CREATE_BY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXTERNAL_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`COMP_EMP_ID`) USING BTREE,
  INDEX `I_COMP_SOURCE`(`COMP_ID`, `OPEN_ID`, `SOURCE`) USING BTREE,
  INDEX `idx_IDNO_COMPID_IDTYPE_DELETEYN_EMPNO`(`ID_NO`, `COMP_ID`, `ID_TYPE`, `DELETE_YN`, `EMP_NO`) USING BTREE,
  INDEX `idx_UPDATETIME`(`update_time`) USING BTREE,
  INDEX `idx_COMPID_EMPNO_DELETEYN`(`COMP_ID`, `EMP_NO`, `DELETE_YN`) USING BTREE,
  INDEX `idx_COMPID_IDNO_IDTYPE_DELETEYN`(`COMP_ID`, `ID_NO`, `ID_TYPE`, `DELETE_YN`) USING BTREE,
  INDEX `idx_COMPID_MOBILE_DELETEYN`(`COMP_ID`, `MOBILE`, `DELETE_YN`) USING BTREE,
  INDEX `t_company_employee_update_time_IDX`(`update_time`, `CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_company_employee_card
-- ----------------------------
DROP TABLE IF EXISTS `t_company_employee_card`;
CREATE TABLE `t_company_employee_card`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `WAGE_CARD_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WAGE_CARD_NUM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ISENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ISDEFAULT` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_company_field
-- ----------------------------
DROP TABLE IF EXISTS `t_company_field`;
CREATE TABLE `t_company_field`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VALUE` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文本内容（域默认值）',
  `RELATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项',
  `RELATION_GROUP` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项所属分组',
  `RELATION_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项名称',
  `SIGNATORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '签署方(SEAL-企业签署方 SIGN-个人签署方)',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '合同签署企业预设字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config_group
-- ----------------------------
DROP TABLE IF EXISTS `t_config_group`;
CREATE TABLE `t_config_group`  (
  `GROUP_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CONFIG_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `GROUP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `GROUP_DESC` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `GROUP_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`GROUP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config_info
-- ----------------------------
DROP TABLE IF EXISTS `t_config_info`;
CREATE TABLE `t_config_info`  (
  `ITEM_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `GROUP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `VALUE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  `ITEM_TYPE` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract
-- ----------------------------
DROP TABLE IF EXISTS `t_contract`;
CREATE TABLE `t_contract`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CONTRACT_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同名称',
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同主体id',
  `HR_CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同记录id',
  `SIGN_MODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签署方式 签章签署、手写签署',
  `SILENCE_SIGN_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '企业静默签署',
  `TEMPLATE_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板id',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `CONTRACT_ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `CONTRACT_FILE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同文件类型',
  `CONTRACT_SIGN_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同签署状态 待签署、签署中、已完成、已终止、已撤回、已退回、已废弃',
  `START_DATE` date NULL DEFAULT NULL COMMENT '发起日期',
  `EXPIRATION_DATE` date NULL DEFAULT NULL COMMENT '完成日期 完成或终止日期',
  `SIGNING_TEMPLATE_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '云签模板编号',
  `UPLOAD_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板上传url',
  `EDIT_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板编辑url',
  `SIGN_CONTRACT_ID` bigint(20) NULL DEFAULT NULL COMMENT '云签文件编号',
  `CREATE_USER_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '发起人id',
  `CREATE_USER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发起人',
  `HANDLE_INFO` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '拒签理由',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_CONTRACT_CREATE`(`CREATED_TIME`) USING BTREE,
  INDEX `IDX_HR_CONTRACT`(`HR_CONTRACT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_fileds
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_fileds`;
CREATE TABLE `t_contract_fileds`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同主体id',
  `CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同id',
  `CONTRACT_STEP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同步骤id',
  `CONTRACT_STEP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同步骤名称',
  `FIELD_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名',
  `RELATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项编码',
  `RELATION_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项名称',
  `RELATION_GROUP` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项分组',
  `FIELD_VALUE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段值',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_CONTRACT_FIELDS`(`COMP_ID`, `TAX_SUB_ID`, `CONTRACT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_flow_step
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_flow_step`;
CREATE TABLE `t_contract_flow_step`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同主体id',
  `CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同id',
  `OPERATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作类型 公章签署、个人签署、抄送',
  `SORTBY` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '步骤顺序',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工id 个人签署时需录入',
  `EMP_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '用工记录id 用于配置域填充',
  `USER_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '用户id 公章签署时需要，企业静默签时无人员',
  `USER_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签署人姓名',
  `STEP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '步骤名称',
  `FLOW_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签名流水号 请求云签使用，FLOW+ID',
  `SIGN_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签名状态 异步查询云签接口',
  `SIGN_URL` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'PC端签署url 准备签名数据后返回',
  `SIGN_URL_MOBILE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '移动端签署url',
  `SIGN_ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `SIGN_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签署时间',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `BATCH_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `TODO_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '平台待办id',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FLOW_STEP_CID`(`CONTRACT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_process
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_process`;
CREATE TABLE `t_contract_process`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同id',
  `CONTRACT_STEP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '当前步骤id',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '步骤的企业员工id',
  `USER_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '步骤的用户id 可空',
  `OPERATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作类型',
  `SORT_BY_CODE` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '步骤顺序',
  `HAS_PROCESSING` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否还有进程',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_COMP_CONTRACT`(`COMP_ID`, `CONTRACT_ID`, `CONTRACT_STEP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_process_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_process_rule`;
CREATE TABLE `t_contract_process_rule`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `NEXT_STEP_ID` int(11) NOT NULL COMMENT '下一步骤id',
  `PROCESS_ID` int(10) UNSIGNED NOT NULL COMMENT '进程id',
  `STEP_ID` int(10) UNSIGNED NOT NULL COMMENT '步骤id',
  `MERCHANT_USER_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否为企业用户',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_PROCESS_STEP`(`PROCESS_ID`, `STEP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_sign
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_sign`;
CREATE TABLE `t_contract_sign`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同id',
  `CONTRACT_STEP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同步骤id',
  `CONTRACT_STEP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同步骤名称',
  `FLOW_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流水号',
  `SIGN_USER_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '签署用户id',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `SIGN_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签署方式 坐标签署',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态 已受理、签署中、签署成功',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CONTRACT_STEP_ID`(`CONTRACT_STEP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_contract_signer
-- ----------------------------
DROP TABLE IF EXISTS `t_contract_signer`;
CREATE TABLE `t_contract_signer`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同主体id',
  `CONTRACT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同id',
  `CONTRACT_STEP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同步骤id',
  `SIGN_IMAGE_ARCHIE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签名图片文档服务id',
  `SIGNER_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签署人名称',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效证件号 身份证号或社会信用代码',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `PHONE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `LEGAL_REPRESENTATIVE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人姓名',
  `LEGAL_REPRESENTATIVE_ID_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人证件号',
  `CONTACTS_PHONE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '企业联系人手机号',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CONTRACT_STEP_ID`(`CONTRACT_STEP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_countries
-- ----------------------------
DROP TABLE IF EXISTS `t_countries`;
CREATE TABLE `t_countries`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `country_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `country_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_update_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_country_code`(`country_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_countries_bk
-- ----------------------------
DROP TABLE IF EXISTS `t_countries_bk`;
CREATE TABLE `t_countries_bk`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `dict_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `dict_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `dict_value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `parent_dict_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `dict_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '国家地区备份表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_custom_config
-- ----------------------------
DROP TABLE IF EXISTS `t_custom_config`;
CREATE TABLE `t_custom_config`  (
  `CONFIG_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CONFIG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_DESC` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`CONFIG_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_department
-- ----------------------------
DROP TABLE IF EXISTS `t_department`;
CREATE TABLE `t_department`  (
  `DEPT_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_DEPT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEPT_SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`DEPT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_dict
-- ----------------------------
DROP TABLE IF EXISTS `t_dict`;
CREATE TABLE `t_dict`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `DICT_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_VALUE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_DICT_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_dict_bk_0521
-- ----------------------------
DROP TABLE IF EXISTS `t_dict_bk_0521`;
CREATE TABLE `t_dict_bk_0521`  (
  `ID` int(10) UNSIGNED NOT NULL,
  `DICT_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_VALUE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_DICT_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DICT_REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_document_classify
-- ----------------------------
DROP TABLE IF EXISTS `t_document_classify`;
CREATE TABLE `t_document_classify`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `DOC_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SORT` int(10) UNSIGNED NULL DEFAULT NULL,
  `PARENT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PATH` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVEL` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_adjust_salary
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_adjust_salary`;
CREATE TABLE `t_emp_adjust_salary`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ADJUST_SALARY_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EFFECTIVE_DATE` date NULL DEFAULT NULL,
  `REMARK` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ADJUST_SALARY_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DATA_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VALID` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `HR_EMPLOYEE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_LEVEL_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '薪级ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_SALARY_LEVEL`(`SALARY_LEVEL_ID`) USING BTREE,
  INDEX `I_ADJUST_EMP_ID`(`EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_care_config
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_care_config`;
CREATE TABLE `t_emp_care_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SEND_SMS_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `EMP_CARE_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NOTIFY_LEADER_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `EMP_TEMPLATE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEADER_TEMPLATE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEND_TIME` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_EMP_CARE_CONFIG_COMP`(`COMP_ID`, `EMP_CARE_TYPE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_care_notify_record
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_care_notify_record`;
CREATE TABLE `t_emp_care_notify_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_CARE_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SMS_SEND_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SMS_CONTENT` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RUN_MODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_ARCHIVE_UPLOAD_TIME`(`COMP_ID`, `CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_import_matching
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_import_matching`;
CREATE TABLE `t_emp_import_matching`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `MATCHING_RULE` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_IMP_MATCHING_COMPID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_import_task
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_import_task`;
CREATE TABLE `t_emp_import_task`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `FILE_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件key',
  `FILE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件名',
  `TASK_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务状态',
  `IMPORT_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '导入类型',
  `MATCHING_RULE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编辑导入时匹配方式',
  `START_TIME` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `END_TIME` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `SUCCESS_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '成功条数',
  `FAILED_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '失败条数',
  `TOTAL_NUMBER` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务总条数',
  `TASK_OWNER` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务所有人',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FIELD_CODES` varchar(900) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TASK_COMPID_CREATE_TIME`(`COMP_ID`, `CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_import_task_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_import_task_detail`;
CREATE TABLE `t_emp_import_task_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TASK_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务id',
  `IMPORT_DATA` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `COL` int(11) NULL DEFAULT NULL COMMENT '错误列',
  `ERROR_MSG` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `TITLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否为表头',
  `ROW` int(10) UNSIGNED NULL DEFAULT NULL,
  `ERROR_COLS` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TASK_DETAIL_TASK_ID`(`TASK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_list_fields
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_list_fields`;
CREATE TABLE `t_emp_list_fields`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `FORM_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类型',
  `FORM_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表名称',
  `OWNER` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '报表所有者用户id',
  `SINGLE_DATA_FILEDS` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单条数据分组字段',
  `MULTI_DATA_GROUP` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '选中的多条分组',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_LIST_OWNER`(`COMP_ID`, `OWNER`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_monthly_ledger
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_monthly_ledger`;
CREATE TABLE `t_emp_monthly_ledger`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_MONTH` date NULL DEFAULT NULL,
  `COMP_INSURED_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SUPPLEMENTARY_PAY_FLAG` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SUPPLEMENTARY_PAY_MONTH` date NULL DEFAULT NULL,
  `REAL_PERSON_SOCIL_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_PERSON_FUND_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_COMP_SOCIAL_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_COMP_FUND_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `LEDGER_SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HR_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_LEDGER_EMP_ID`(`EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_monthly_ledger_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_monthly_ledger_detail`;
CREATE TABLE `t_emp_monthly_ledger_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `EMP_LEDGER_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `INSURED_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INSURANCE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BASE_NUMBER` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `PERSON_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PERSON_PAY_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_SCALE` decimal(10, 4) NULL DEFAULT NULL,
  `COMP_FIXED_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COMP_PAY_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_PAY_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_DETAIL_EMP_LEDGER_ID`(`EMP_LEDGER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_notify_accept
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_notify_accept`;
CREATE TABLE `t_emp_notify_accept`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `EMP_RECORD_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工用工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `EMP_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '接收转正通知员工姓名',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有效',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '转正通知接收人员(其他员工)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_notify_config
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_notify_config`;
CREATE TABLE `t_emp_notify_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `NOTIFY_STATUS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '通知开启状态(1为开启、0为关闭)',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有效(1为有效、0为失效)',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工转正通知配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_wx_notify_switch
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_wx_notify_switch`;
CREATE TABLE `t_emp_wx_notify_switch`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `NOTIFY_STATUS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '通知开启状态(1为开启、0为关闭)',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否有效(1为有效、0为失效)',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `idx_unique_c_e`(`COMP_ID`, `EFFECTIVE_YN`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '微信消息通知开关配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_employee`;
CREATE TABLE `t_employee`  (
  `EMP_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_SEX` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTHDAY` date NULL DEFAULT NULL,
  `IN_WORK_DAY` date NULL DEFAULT NULL,
  `NATIONALITY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIRTH_PLACE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MARITAL_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EDUCATION` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MAJOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `GRADUATE_SCHOOL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HOME_ADDRESS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACTS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TELEPHONE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WAGE_CARD_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WAGE_CARD_NUM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `EXTRA_DATA` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DISABILITY_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'N',
  `MARTYR_FAMILY_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'N',
  `LONELY_OLD_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'N',
  `DISABILITY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MARTYR_FAMILY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_DAY` date NULL DEFAULT NULL,
  `ZZ_STATUS` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZZ_DAY` date NULL DEFAULT NULL,
  `LEAVE_DAY` date NULL DEFAULT NULL,
  `LEAVE_REASON` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `POSITION_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGULAR_EMP_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TURN_REGULAR_DATE` date NULL DEFAULT NULL,
  `WORK_CITY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORKER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVALID_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `OTHER_ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OTHER_ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RELATED_REASON` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FIRST_ENTRY_TIME` date NULL DEFAULT NULL,
  `EXPECT_LEAVE_TIME` date NULL DEFAULT NULL,
  `ENGLISH_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ADJUST_COMP_YEARS` int(11) NULL DEFAULT NULL,
  `HOUSEHOLD_COUNTRY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HOUSEHOLD_REGISTRATION_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEPARTMENT_ID` int(11) NULL DEFAULT NULL,
  `POST_ID` int(11) NULL DEFAULT NULL,
  `CITIC_PAY_PROTOCOL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开薪易代发协议号',
  `DISABILITY_TYPE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '残疾证件类型',
  PRIMARY KEY (`EMP_ID`) USING BTREE,
  INDEX `I_COMP_EMP_ID`(`COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_employee_work_his
-- ----------------------------
DROP TABLE IF EXISTS `t_employee_work_his`;
CREATE TABLE `t_employee_work_his`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `WORK_START_DAY` date NULL DEFAULT NULL,
  `WORK_END_DAY` date NULL DEFAULT NULL,
  `JOB_SEQ_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JOB_TITLE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JOB_LEVEL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORK_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OPER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `REMARK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_feishu_app_info
-- ----------------------------
DROP TABLE IF EXISTS `t_feishu_app_info`;
CREATE TABLE `t_feishu_app_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `APP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_SECRET` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_TICKET` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENCRYPT_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERIFICATION_TOKEN` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOURCE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_fex_tsk
-- ----------------------------
DROP TABLE IF EXISTS `t_fex_tsk`;
CREATE TABLE `t_fex_tsk`  (
  `TSK_ID` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务ID',
  `INTERFACE_NM` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '接口名称',
  `EXC_DT` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '计划执行日期',
  `EXC_ST` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'C' COMMENT '执行状态 C-未执行,F-失败,S-成功',
  `START_TIME` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据起始时间',
  `END_TIME` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据结束时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`TSK_ID`) USING BTREE,
  INDEX `t_fex_tsk_EXC_ST_IDX`(`EXC_ST`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_field_mapping
-- ----------------------------
DROP TABLE IF EXISTS `t_field_mapping`;
CREATE TABLE `t_field_mapping`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '公司ID',
  `SYSTEM_FIELD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '系统字段',
  `MAPPING_FIELD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '映射字段',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工资条-系统匹配字段映射关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_formula_items
-- ----------------------------
DROP TABLE IF EXISTS `t_formula_items`;
CREATE TABLE `t_formula_items`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `FORMULA_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ITEM_CATEGORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VALUE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONDITION_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_CONFIG_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SHOW_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_ITEMS_FORMULA_ID`(`FORMULA_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_help_document
-- ----------------------------
DROP TABLE IF EXISTS `t_help_document`;
CREATE TABLE `t_help_document`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `DOC_TITLE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DOC_CLASSIFY_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DOC_DESC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SORT` int(10) UNSIGNED NULL DEFAULT NULL,
  `CONTENT` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `PAGE_VIEW` int(10) UNSIGNED NULL DEFAULT NULL,
  `PICTURE_URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_department
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_department`;
CREATE TABLE `t_hr_department`  (
  `DEPARTMENT_ID` int(10) UNSIGNED NOT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `DEPARTMENT_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
  `PARENT_DEPARTMENT_ID` int(11) NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ID_LINK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME_LINK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  PRIMARY KEY (`DEPARTMENT_ID`) USING BTREE,
  INDEX `IDX_DEPART_COMP_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_contact_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_contact_msg`;
CREATE TABLE `t_hr_emp_contact_msg`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `PERSONAL_EMAIL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个人邮箱',
  `WORK_EMAIL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作邮箱',
  `ADRESS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '现居住地详细地址',
  `EMERGENCY_PERSON` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '紧急联系人姓名',
  `EMERGENCY_NUMBER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '紧急联系人电话',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MSG_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_contract
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_contract`;
CREATE TABLE `t_hr_emp_contract`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工id',
  `COMP_ID` int(11) NULL DEFAULT NULL COMMENT '企业id',
  `CONTRACT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同编号',
  `CONTRACT_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '合同主体',
  `CONTRACT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同类型',
  `CONTRACT_TERM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同期限',
  `CONTRACT_START_DATE` date NULL DEFAULT NULL COMMENT '合同开始日期',
  `CONTRACT_END_DATE` date NULL DEFAULT NULL COMMENT '合同结束日期',
  `CONTRACT_SIGN_DATE` date NULL DEFAULT NULL COMMENT '合同签订日期',
  `CONTRACT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同状态',
  `SIGN_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '新、续签',
  `SIGN_REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同备注',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `EMP_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '关联用工记录id',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效状态',
  `CONFIRM_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '确认状态',
  `CONTRACT_TERM_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同期限单位',
  `RENEWAL_COUNT` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '续签次数',
  `RECORD_SIGN_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同记录状态',
  `RENEWAL_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_CONTRACT_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_CONTRACT_RECORD`(`EMP_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_custom_info
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_custom_info`;
CREATE TABLE `t_hr_emp_custom_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `EXTEND_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'EXTEND_INFO',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'version',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_CUSTOM_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_education
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_education`;
CREATE TABLE `t_hr_emp_education`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `SCHOOL` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '毕业院校',
  `MAJOR` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '专业',
  `EDUCATIONAL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学历',
  `EDU_DEGREE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学位',
  `EDU_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '教学方式',
  `ENTRANCE_TIME` date NULL DEFAULT NULL COMMENT '入学时间',
  `GRADUATION_TIME` date NULL DEFAULT NULL COMMENT '毕业时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'version',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_EDU_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_family_info
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_family_info`;
CREATE TABLE `t_hr_emp_family_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `RELATIONSHIP` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关系',
  `GENDER` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '性别',
  `FAMILY_MOBILE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电话',
  `COMPANY_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作单位',
  `POSITION` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '职务',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'version',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FAMILY_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_file
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_file`;
CREATE TABLE `t_hr_emp_file`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `PROFILE_PICTURE` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件照',
  `DIPLOMA` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '毕业证书',
  `DEGREE_CERT` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学位证书',
  `DIMISSION_FILE` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职证明',
  `BANK_CARD` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行卡照',
  `ONE_SIZE_PHOTO` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一寸照',
  `LIFE_PHOTO` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生活照',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FILE_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_position_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_position_record`;
CREATE TABLE `t_hr_emp_position_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业人员id',
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `DEPARTMENT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '部门id',
  `POSITION_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '岗位id',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `RANK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_TO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '直属上级',
  `WORK_CITY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作城市',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `START_DATE` date NULL DEFAULT NULL COMMENT '开始日期',
  `END_DATE` date NULL DEFAULT NULL COMMENT '结束日期',
  `EMP_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '关联用工记录id',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效状态',
  `CONFIRM_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '确认状态',
  `LAST_RECORD` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '变动前记录id',
  `TRANSFER_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗类型',
  `TRANSFER_TIME` timestamp NULL DEFAULT NULL COMMENT '调岗时间',
  `TRANSFER_REASON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗原因',
  `ORG_RANK_ID` int(11) NULL DEFAULT NULL,
  `ORG_POST_ID` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_HR_POSI_COMP_EMP_ID`(`COMP_EMP_ID`) USING BTREE,
  INDEX `idx_EMP_RECORD_ID`(`EMP_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_rule`;
CREATE TABLE `t_hr_emp_rule`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL COMMENT '企业ID',
  `TAX_SUB_ID` int(11) NOT NULL COMMENT '公司ID',
  `RULE_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '规则名称',
  `DESCRIPTION` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '规则描述',
  `UID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标识',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '二维码规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_emp_work_his
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_emp_work_his`;
CREATE TABLE `t_hr_emp_work_his`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(11) NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(11) NOT NULL COMMENT '企业id',
  `START_DATE` date NULL DEFAULT NULL COMMENT '开始时间',
  `END_DATE` date NULL DEFAULT NULL COMMENT '结束时间',
  `COMPANY_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作单位',
  `POSITION` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '职务',
  `COMPETE_AGREEMENT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有无竞业限制',
  `REFERENCE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证明人',
  `REFERENCE_MOBILE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证明人联系电话',
  `REASON` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职原因',
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作备注',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'version',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_HIS_COMP_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_employee`;
CREATE TABLE `t_hr_employee`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `EMP_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '员工状态',
  `EMP_NATURE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用工性质(全职、兼职、实习、劳务、退休返聘)',
  `TURN_REGULAR_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正状态',
  `ENTRY_DATE` date NULL DEFAULT NULL COMMENT '入职日期',
  `ADJUST_COMP_YEARS` int(11) NULL DEFAULT NULL COMMENT '调整司龄',
  `REGIST_FORM_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表填写状态',
  `OFFER_DATE` date NULL DEFAULT NULL COMMENT '邀请日期',
  `TURN_REGULAR_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正类型',
  `TRIAL_PERIOD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '试用期限',
  `TURN_REGULAR_DATE` date NULL DEFAULT NULL COMMENT '转正日期',
  `LAST_WOKING_DAY` date NULL DEFAULT NULL COMMENT '最后工作日',
  `CHANNEL` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '招聘渠道',
  `LEAVE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职类型',
  `LEAVE_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职原因',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `APPLY_DATE` date NULL DEFAULT NULL COMMENT '离职申请日期',
  `CONFIRM_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '确认状态',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有效',
  `REMARK` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正备注',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `RETURN_REASON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '退回原因',
  `LEAVE_REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职备注',
  `ALLOW_DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否允许删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_HR_COMP_ID_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_HR_EMPLOYEE_ENTRY`(`ENTRY_DATE`) USING BTREE,
  INDEX `idx_COMPEMPID`(`COMP_EMP_ID`) USING BTREE,
  INDEX `t_hr_employee_CREATE_TIME_IDX`(`CREATE_TIME`, `update_time`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_employee_delete_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_employee_delete_record`;
CREATE TABLE `t_hr_employee_delete_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_EMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业员工id',
  `COMP_ID` int(10) UNSIGNED NOT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `EMP_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '员工状态',
  `EMP_NATURE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用工性质(全职、兼职、实习、劳务、退休返聘)',
  `TURN_REGULAR_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正状态',
  `ENTRY_DATE` date NULL DEFAULT NULL COMMENT '入职日期',
  `ADJUST_COMP_YEARS` int(11) NULL DEFAULT NULL COMMENT '调整司龄',
  `REGIST_FORM_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表填写状态',
  `OFFER_DATE` date NULL DEFAULT NULL COMMENT '邀请日期',
  `TURN_REGULAR_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正类型',
  `TRIAL_PERIOD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '试用期限',
  `TURN_REGULAR_DATE` date NULL DEFAULT NULL COMMENT '转正日期',
  `LAST_WOKING_DAY` date NULL DEFAULT NULL COMMENT '最后工作日',
  `CHANNEL` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '招聘渠道',
  `LEAVE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职类型',
  `LEAVE_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职原因',
  `CUSTOM_FIELDS` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义字段json',
  `APPLY_DATE` date NULL DEFAULT NULL COMMENT '离职申请日期',
  `CONFIRM_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '确认状态',
  `EFFECTIVE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有效',
  `REMARK` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正备注',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `RETURN_REASON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '退回原因',
  `LEAVE_REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职备注',
  `ALLOW_DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否允许删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_HR_COMP_ID_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_HR_EMPLOYEE_ENTRY`(`ENTRY_DATE`) USING BTREE,
  INDEX `idx_COMPEMPID`(`COMP_EMP_ID`) USING BTREE,
  INDEX `t_hr_employee_delete_record_CREATE_TIME_IDX`(`CREATE_TIME`, `update_time`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_employee_label
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_employee_label`;
CREATE TABLE `t_hr_employee_label`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工ID',
  `LABEL_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '标签ID',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_EMP_LABEL`(`COMP_ID`, `COMP_EMP_ID`, `LABEL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业员工标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_entry_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_entry_record`;
CREATE TABLE `t_hr_entry_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL COMMENT '企业ID',
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `TAX_SUB_ID` int(11) NULL DEFAULT NULL COMMENT '公司',
  `DEPARTMENT_ID` int(11) NULL DEFAULT NULL COMMENT '部门',
  `POST_ID` int(11) NULL DEFAULT NULL COMMENT '岗位',
  `AUDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核状态',
  `EMP_RECORD_ID` int(11) NULL DEFAULT NULL COMMENT '用工记录ID',
  `ENTRY_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职方式',
  `ENTRY_DATE` date NULL DEFAULT NULL COMMENT '预入职日期',
  `EFFECTIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效状态',
  `SPONSOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人',
  `SPONSOR_ID` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
  `AUDIT_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单号',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ENTRY_EMP_RECORD_ID`(`EMP_RECORD_ID`) USING BTREE,
  INDEX `IDX_ENTRY_MOBILE`(`COMP_ID`, `MOBILE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_leave_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_leave_record`;
CREATE TABLE `t_hr_leave_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL COMMENT '企业ID',
  `COMP_EMP_ID` int(11) NOT NULL COMMENT '企业员工ID',
  `EMP_RECORD_ID` int(11) NOT NULL COMMENT '用工记录ID',
  `DEPARTMENT_ID` int(11) NULL DEFAULT NULL COMMENT '部门ID',
  `POST_ID` int(11) NULL DEFAULT NULL COMMENT '岗位ID',
  `LEAVE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职类型',
  `LAST_WOKING_DAY` date NULL DEFAULT NULL COMMENT '离职日期',
  `ENTRY_DATE` date NULL DEFAULT NULL COMMENT '入职日期',
  `LEAVE_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职原因',
  `REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `LEAVE_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职方式',
  `AUDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核状态',
  `EFFECTIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效状态',
  `SPONSOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人',
  `SPONSOR_ID` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
  `AUDIT_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单号',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_LEAVE_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_LEAVE_EMP_RECORD_ID`(`EMP_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_post
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_post`;
CREATE TABLE `t_hr_post`  (
  `POST_ID` int(10) UNSIGNED NOT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `POST_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '岗位名称',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `DELETE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  PRIMARY KEY (`POST_ID`) USING BTREE,
  INDEX `IDX_POST_COM_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_regular_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_regular_record`;
CREATE TABLE `t_hr_regular_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL COMMENT '企业ID',
  `COMP_EMP_ID` int(11) NOT NULL COMMENT '企业员工ID',
  `EMP_RECORD_ID` int(11) NOT NULL COMMENT '用工记录ID',
  `TURN_REGULAR_DATE` date NULL DEFAULT NULL COMMENT '转正日期',
  `TURN_REGULAR_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正类型',
  `TRIAL_PERIOD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '试用期限',
  `REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `TURN_REGULAR_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '转正方式,手动调岗，审批调岗',
  `AUDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核状态',
  `EFFECTIVE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生效状态',
  `SPONSOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人',
  `SPONSOR_ID` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
  `EFFECTIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '记录有效状态',
  `AUDIT_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单号',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_REGULAR_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_REGULAR_EMP_RECORD_ID`(`EMP_RECORD_ID`) USING BTREE,
  INDEX `IDX_REGULAR_TIME`(`TURN_REGULAR_DATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_hr_transfer_record
-- ----------------------------
DROP TABLE IF EXISTS `t_hr_transfer_record`;
CREATE TABLE `t_hr_transfer_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL COMMENT '企业ID',
  `COMP_EMP_ID` int(11) NOT NULL COMMENT '企业员工ID',
  `EMP_RECORD_ID` int(11) NOT NULL COMMENT '用工记录ID',
  `TRANSFER_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗类型',
  `TRANSFER_TIME` date NULL DEFAULT NULL COMMENT '调岗时间',
  `BEFORE_DEPARTMENT_ID` int(11) NULL DEFAULT NULL COMMENT '调岗前部门',
  `BEFORE_POST_ID` int(11) NULL DEFAULT NULL COMMENT '调岗前岗位',
  `BEFORE_RANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗前职级',
  `BEFORE_WORK_CITY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗前工作城市',
  `BEFORE_REPORT` int(11) NULL DEFAULT NULL COMMENT '调岗前上级',
  `AFTER_DEPARTMENT_ID` int(11) NULL DEFAULT NULL COMMENT '调岗后部门',
  `AFTER_POST_ID` int(11) NULL DEFAULT NULL COMMENT '调岗后岗位',
  `AFTER_RANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗后职级',
  `AFTER_WORK_CITY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗后工作城市',
  `AFTER_REPORT` int(11) NULL DEFAULT NULL COMMENT '调岗后上级',
  `REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `TRANSFER_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调岗方式,手动调岗，审批调岗',
  `AUDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核状态',
  `EFFECTIVE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生效状态',
  `EFFECTIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '记录有效状态',
  `SPONSOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人',
  `SPONSOR_ID` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
  `AUDIT_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单号',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `BEFORE_ORG_RANK` int(11) NULL DEFAULT NULL,
  `AFTER_ORG_RANK` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TRANSFER_EMP_ID`(`COMP_ID`, `COMP_EMP_ID`) USING BTREE,
  INDEX `IDX_TRANSFER_EMP_RECORD_ID`(`EMP_RECORD_ID`) USING BTREE,
  INDEX `IDX_TRANSFER_TIME`(`TRANSFER_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_industry
-- ----------------------------
DROP TABLE IF EXISTS `t_industry`;
CREATE TABLE `t_industry`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `data` json NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '行业表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_inspection_applicant
-- ----------------------------
DROP TABLE IF EXISTS `t_inspection_applicant`;
CREATE TABLE `t_inspection_applicant`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DJXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NSRSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZGSWJMC` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DJZCMC` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LXDH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LXDZ` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DJRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ISDEFAULT` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ZGSWSKFJ` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税局字段 主管税务机关科所名称',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_APPLICANT_TAX_SUB_ID`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_label
-- ----------------------------
DROP TABLE IF EXISTS `t_label`;
CREATE TABLE `t_label`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `LABEL_UK_MERCHANT_ID_NAME`(`MERCHANT_ID`, `NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_map_config
-- ----------------------------
DROP TABLE IF EXISTS `t_map_config`;
CREATE TABLE `t_map_config`  (
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `L_KEY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `R_KEY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DATA_SRC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_cibk_auth
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_cibk_auth`;
CREATE TABLE `t_merchant_cibk_auth`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OPERATOR_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACTS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTH_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AGREEMENT_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTH_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `NONCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_MER_AUTH_PAYER_NO`(`TAX_PAYER_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_out_of_bank_operate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_out_of_bank_operate_record`;
CREATE TABLE `t_out_of_bank_operate_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '操作人ID',
  `OPERATOR_INFO` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人相关信息',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_out_of_bank_operate_record_USER_ID_IDX`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '法人实体认证审计记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_batch
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_batch`;
CREATE TABLE `t_pay_batch`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `APPLY_CERTIFICATION_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UNIQUE_IDENTIFY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ELECTRONIC_TAX_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_OPERATION_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_OPERATION_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INCOME_MONTH` date NULL DEFAULT NULL,
  `DECLARE_FORM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_PAY_START_TIME` datetime NULL DEFAULT NULL,
  `TAX_PAY_END_TIME` datetime NULL DEFAULT NULL,
  `PAID_IN_TAX_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PAID_IN_TAX_AMOUNT_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `RESUB_OR_REFUND_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `RESUB_OR_REFUND_AMOUNT_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `PAY_DEADLINE` date NULL DEFAULT NULL,
  `PAY_DATE` date NULL DEFAULT NULL,
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_INFO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_SYNC_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ASYNC_REQUEST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `REPORT_MONTH` date NULL DEFAULT NULL,
  `REPORT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_detail`;
CREATE TABLE `t_pay_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `PAY_BATCH_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `APPLY_CERTIFICATION_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UNIQUE_IDENTIFY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APPLY_CERTIFICATION_DETAIL_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COLLECTION_TREASURY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVY_TAX_OFFICE_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVY_TAX_ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVY_TAX_ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVY_TAX_PART_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEVY_TAX_PART_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RATE` decimal(10, 4) NULL DEFAULT NULL,
  `TAX_PAY_START_TIME` datetime NULL DEFAULT NULL,
  `TAX_PAY_END_TIME` datetime NULL DEFAULT NULL,
  `PAY_DEADLINE` date NULL DEFAULT NULL,
  `PAID_IN_TAX_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_ORG_COED` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_ORG_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_stubs
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_stubs`;
CREATE TABLE `t_pay_stubs`  (
  `PAY_STUBS_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEPT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXT_DATA` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `SALARY_MONTH` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_YEAR` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PAY_STUBS_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PAY_STUBS_REMARK` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CALCULATE_TOTAL_SALARY` decimal(10, 2) NULL DEFAULT NULL,
  `ID_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEND_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送工资条时间（pay_status= PROVIDED)',
  `VIEWED` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否已查看',
  `CONFIRMED` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否已确认，1-无需确认 2-未确认 3-已确认',
  `SALARY_BATCH_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`PAY_STUBS_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_stubs_config
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_stubs_config`;
CREATE TABLE `t_pay_stubs_config`  (
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_CONFIG_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  UNIQUE INDEX `SALARY_RULE_ID`(`SALARY_RULE_ID`, `SALARY_CONFIG_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_payroll_archive
-- ----------------------------
DROP TABLE IF EXISTS `t_payroll_archive`;
CREATE TABLE `t_payroll_archive`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TAX_PAYER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DOC_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_MONTH` date NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_payroll_batch_info
-- ----------------------------
DROP TABLE IF EXISTS `t_payroll_batch_info`;
CREATE TABLE `t_payroll_batch_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MONTH_DATE` date NULL DEFAULT NULL,
  `APPLY_DATE` date NULL DEFAULT NULL,
  `PAYROLL_TYPE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYROLL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYROLL_DATE` date NULL DEFAULT NULL,
  `TOTAL_COUNT` int(11) NULL DEFAULT NULL,
  `TOTAL_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `SUCCESS_COUNT` int(11) NULL DEFAULT NULL,
  `SUCCESS_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `BANK_BATCH_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_PAYROLL_BATCH_COMP_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_payroll_detail_info
-- ----------------------------
DROP TABLE IF EXISTS `t_payroll_detail_info`;
CREATE TABLE `t_payroll_detail_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `BATCH_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WAGE_CARD_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WAGE_CARD_NUM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `PAYROLL_TYPE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYROLL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERROR_MSG` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `EMP_SALARY_CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `NON_RESIDENT` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_PAYROLL_DETAIL_BATCH_ID`(`BATCH_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_platform_user
-- ----------------------------
DROP TABLE IF EXISTS `t_platform_user`;
CREATE TABLE `t_platform_user`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `USER_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `USER_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `USER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_PLATFORM_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_preinstall_item
-- ----------------------------
DROP TABLE IF EXISTS `t_preinstall_item`;
CREATE TABLE `t_preinstall_item`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ITEM_CATEGORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TIP_EXPLAIN` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_price_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_price_plan`;
CREATE TABLE `t_price_plan`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `PRICE_PLAN_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PLAN_MODEL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PERSON_COUNT` int(10) UNSIGNED NULL DEFAULT 0,
  `PERSON_FLOAT` int(10) UNSIGNED NULL DEFAULT 0,
  `DAYS_FLOAT` int(10) UNSIGNED NULL DEFAULT 0,
  `TRIAL_PERIOD` int(10) UNSIGNED NULL DEFAULT 0,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `PRICE_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRICE` decimal(10, 2) NULL DEFAULT NULL,
  `VALUE_ADD_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `MONTH_PRICE` decimal(10, 2) NULL DEFAULT NULL,
  `YEAR_PRICE` decimal(10, 2) NULL DEFAULT NULL,
  `EFFECTIVE_DAYS` int(10) UNSIGNED NULL DEFAULT NULL,
  `PLAN_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_reduction_dict
-- ----------------------------
DROP TABLE IF EXISTS `t_reduction_dict`;
CREATE TABLE `t_reduction_dict`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SDXM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMFS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMSXMC` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMSXXZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_attend_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_attend_plan`;
CREATE TABLE `t_salary_attend_plan`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `PLAN_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方案名称',
  `PLAN_CATEGORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方案类别',
  `REMARK` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_PLAN_COMP_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤算薪方案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_attend_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_attend_rule`;
CREATE TABLE `t_salary_attend_rule`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `PLAN_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '方案id',
  `PLAN_CATEGORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方案类别 考勤计薪、请假扣款、补贴规则',
  `RULE_CATEGORY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '规则类型 迟到扣款、早退扣款、缺卡扣款',
  `DEDUCT_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款规则 按分钟扣款、按次数扣款、按固定金额扣款、按固定比例扣款',
  `DEDUCT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款类型',
  `UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单位 天、小时',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_RULE_PLAN_ID`(`PLAN_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤算薪方案规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_attend_rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_attend_rule_detail`;
CREATE TABLE `t_salary_attend_rule_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '规则表id',
  `PLAN_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '方案id',
  `DEDUCT_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款规则 冗余字段(按分钟扣款、按次数扣款、按固定金额扣款、按固定比例扣款)',
  `DETAIL_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '类型 阶梯型、按单次扣款、按累计次数扣固定金额、按累计次数扣工资比例',
  `PAID_DAY_CONVERT_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '计薪天数折算标准 按工作日折算、按算薪日折算、按自然人折算、自定义',
  `CUSTOM_VALUE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '计薪天数折算自定义值 自定义天数、每天工作时长',
  `AMOUNT` decimal(10, 2) NULL DEFAULT NULL COMMENT '金额',
  `MINIMUM` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最小',
  `MAXIMUM` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最大',
  `DEDUCT_RATIO` decimal(10, 2) NULL DEFAULT NULL COMMENT '扣款比例',
  `DEDUCT_ITEM` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款项目 扣款计薪基数、全勤扣款规则的选项卡',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_RULE_DETAIL_RULE_ID`(`RULE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤算薪方案规则明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_batch
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_batch`;
CREATE TABLE `t_salary_batch`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '公司ID',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工资条批次名称',
  `SALARY_PAYMENT_MONTH` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工资月份（年-月如2022-03）',
  `PAYMENT_STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发放状态(NO_PROVIDE-待发放, PROVIDED-已发放, BACK-已撤回)',
  `OPERATOR` bigint(20) NOT NULL COMMENT '操作人',
  `PAYROLL_NOW` tinyint(4) NULL DEFAULT NULL COMMENT '是否为预约发放',
  `PAYMENT_TIME` timestamp NULL DEFAULT NULL COMMENT '发放时间',
  `SOURCE` tinyint(4) NOT NULL COMMENT '来源(1-导入工资条文件,2-选择薪酬工资表)',
  `MAPPINGS` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资表匹配字段映射关系',
  `ITEMS` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工资表表头及其所属组',
  `VISIBLE_ITEMS` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '查看工资条时显示的项目集合',
  `HIDE_ZERO` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否隐藏工资条中值为0的工资项',
  `HIDE_NULL` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否隐藏工资条中值为空的项',
  `REMARK` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资条说明',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `CONFIRM_TYPE` tinyint(4) NOT NULL DEFAULT 1 COMMENT '员工收到工资单后，如何完成确认操作(1-无需确认 2-点击确认)',
  `AUTO_CONFIRM_TIME` timestamp NULL DEFAULT NULL COMMENT '自动确认时间',
  `SALARY_CHECK_ID` bigint(20) NULL DEFAULT NULL COMMENT '当上传方式为选择薪酬表时，该值为所选的salaryCheckId',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工资条批次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_check
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_check`;
CREATE TABLE `t_salary_check`  (
  `CHECK_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CHECK_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_STATUS` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_MONTH` date NULL DEFAULT NULL,
  `EMP_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `IN_WORK_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `CHANGE_JOB_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMPARE_PRE_INCR_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMPARE_PRE_DECR_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `CALCULATE_TOTAL_SALARY` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_TOTAL_SALARY` decimal(10, 2) NULL DEFAULT NULL,
  `COMPARE_PRE_LEAVE_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMPARE_PRE_ZZ_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMPARE_PRE_SALARY_TOTAL` int(10) UNSIGNED NULL DEFAULT NULL,
  `PAYROLL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_MONTH` date NULL DEFAULT NULL,
  PRIMARY KEY (`CHECK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_check_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_check_employee`;
CREATE TABLE `t_salary_check_employee`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEPT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_WORK_HIS_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PAY_SALARY_MONTH` date NULL DEFAULT NULL,
  `PAY_SALARY_TIMES` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `WORK_START_DAY` date NULL DEFAULT NULL,
  `CACULATE_TOTAL_SALARY` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_TOTAL_SALARY` decimal(10, 2) NULL DEFAULT NULL,
  `JOB_TITLE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORK_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXT_DATA` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FAIL_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYROLL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RULE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HR_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CITIC_PAY_PROTOCOL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开薪易代发协议号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_config
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_config`;
CREATE TABLE `t_salary_config`  (
  `SALARY_CONFIG_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CONFIG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DATA_SRC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_GROUP` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTENT` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  `CONFIG_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VALUE_ITEM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXT_DATA` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`SALARY_CONFIG_ID`) USING BTREE,
  INDEX `IDX_SALARY_RULE_CONFIG_RULE_ID`(`SALARY_RULE_ID`) USING BTREE,
  INDEX `IDX_SALARY_RULE_CONFIG_CODE`(`CONFIG_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_config_formula
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_config_formula`;
CREATE TABLE `t_salary_config_formula`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SALARY_CONFIG_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_FORMULA_CONFIG_ID`(`SALARY_CONFIG_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_config_formula_condition
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_config_formula_condition`;
CREATE TABLE `t_salary_config_formula_condition`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SALARY_CONFIG_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `FORMULA_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CONTIDITION_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONNECTOR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_CONDITION_FORMULA_ID`(`FORMULA_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_detail_export
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_detail_export`;
CREATE TABLE `t_salary_detail_export`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '公司ID',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '手机号',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '证件号码',
  `MATCH_STATUS` tinyint(4) NOT NULL DEFAULT 0 COMMENT '花名册匹配状态',
  `CHECK_STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '校验状态(NOT_VERIFIED-未校验 ACCESS-校验通过 FAIL-校验失败)',
  `CHECK_MESSAGE` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实发工资',
  `SALARY_BATCH_ID` bigint(20) NOT NULL COMMENT '工资条批次ID',
  `EXTRA_DATA` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工资表导入明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_level
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_level`;
CREATE TABLE `t_salary_level`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '企业ID',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '薪级名',
  `SALARY_MIN` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '最低工资',
  `SALARY_MAX` decimal(11, 2) NULL DEFAULT NULL COMMENT '最高工资',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_COMP_NAME`(`COMP_ID`, `NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '薪级薪档' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_range
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_range`;
CREATE TABLE `t_salary_range`  (
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `RANGE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UNIQUE_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_rule`;
CREATE TABLE `t_salary_rule`  (
  `SALARY_RULE_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SALARY_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RULE_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_PEIOD_MONTH` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_PEIOD_DAY` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_SALARY_MONTH` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_SALARY_DAY` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IN_OUT_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_CHANGE_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MULTI_SALARY_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `SALARY_REMARK` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STUBS_SHOW_RULES` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SALARY_SHOW_TIPS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_BELONG_MONTH` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ATTEND_PLANS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '考勤相关扣款方案',
  `ENABLE_USER_AUTH` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否设置了工资负责人',
  `CREATE_BY` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
  PRIMARY KEY (`SALARY_RULE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_schedule
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_schedule`;
CREATE TABLE `t_salary_schedule`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CHECK_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `MONTH_DATE` date NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `REDUCTION_ITEM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `SOURCE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SUB_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_SCHEDULE_CHECK`(`CHECK_ID`) USING BTREE,
  INDEX `I_SCHEDULE_TAXSUB_DATE`(`TAX_SUB_ID`, `MONTH_DATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_schedule_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_schedule_detail`;
CREATE TABLE `t_salary_schedule_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TOTAL_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `MONTH_DATE` date NULL DEFAULT NULL,
  `XM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SDXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMSX` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMXZ` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JMJE` decimal(10, 2) NULL DEFAULT NULL,
  `REDUCTION_ITEM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SYSBM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BDSXRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NDBF` decimal(10, 2) NULL DEFAULT NULL,
  `YDBF` decimal(10, 2) NULL DEFAULT NULL,
  `BQKCJE` decimal(10, 2) NULL DEFAULT NULL,
  `SYYLZHBH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SBKCYF_Q` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SBKCYF_Z` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BSJYM` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SZDWMC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SZDWNSRSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JZPZH` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JZRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JZJE` decimal(18, 2) NULL DEFAULT NULL,
  `KCBL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SJJZE` decimal(18, 2) NULL DEFAULT NULL,
  `BZ` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `JZKC_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `JZKC_ERRORINFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ERRORINFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_SCHEDULE_DETAIL_TOTAL`(`TOTAL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_setting
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_setting`;
CREATE TABLE `t_salary_setting`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '公司ID',
  `CONFIRM_TYPE` tinyint(4) NOT NULL DEFAULT 1 COMMENT '员工收到工资单后，如何完成确认操作(1-无需确认 2-点击确认)',
  `AUTO_MATCH` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否记住匹配字段',
  `TIMEOUT` int(11) NOT NULL DEFAULT 48 COMMENT '系统自动确认时间',
  `TIMEUNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '系统自动确认时间单位(SECOND-秒 MINUTE-分钟 HOUR-小时 DAY-天 )',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工资条设置（公司维度，非工资条维度）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_tax_item_config
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_tax_item_config`;
CREATE TABLE `t_salary_tax_item_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `REPORT_ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ITEM_CODE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ITEM_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTENT` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_SCHEDULE_RULE_ITEM`(`SALARY_RULE_ID`, `REPORT_ITEM_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_salary_tax_rule_config
-- ----------------------------
DROP TABLE IF EXISTS `t_salary_tax_rule_config`;
CREATE TABLE `t_salary_tax_rule_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SALARY_RULE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RULE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTENT` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_social_security_benefits
-- ----------------------------
DROP TABLE IF EXISTS `t_social_security_benefits`;
CREATE TABLE `t_social_security_benefits`  (
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `PENSION_INSURANCE_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `MEDICAL_INSURANCE_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `UNEMPLOYMENT_INSURANCE_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `SERIOUS_MEDICAL_INSURANCE_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `SOCIAL_SECURITY_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `PENSION_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `MEDICAL_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `UNEMPLOYMENT_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `INDUSTRIAL_INJURY_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `BIRTH_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `SERIOUS_MEDICAL_INSURANCE_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `SOCIAL_SECURITY_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `SOCIAL_SECURITY_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `HOUSING_FUND_PERSON` decimal(10, 2) NULL DEFAULT NULL,
  `HOUSING_FUND_COMP` decimal(10, 2) NULL DEFAULT NULL,
  `HOUSING_FUND_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `DISABILITY_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `DATA_MONTH` date NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `DATA_SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HR_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_special_deduction_info
-- ----------------------------
DROP TABLE IF EXISTS `t_special_deduction_info`;
CREATE TABLE `t_special_deduction_info`  (
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TOTAL_CHILDREN_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_FURTHER_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_YYEZHFZC` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_HOME_LOADS` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_HOUSE_RENT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_SUPPORT_PARENTS` decimal(10, 2) NULL DEFAULT NULL,
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_DAY` date NULL DEFAULT NULL,
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SYNC_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `DATA_MONTH` date NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `TAX_REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PERSON_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MONTH_CHILDREN_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `MONTH_FURTHER_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `MONTH_YYEZHFZC` decimal(10, 2) NULL DEFAULT NULL,
  `MONTH_HOME_LOADS` decimal(10, 2) NULL DEFAULT NULL,
  `MONTH_HOUSE_RENT` decimal(10, 2) NULL DEFAULT NULL,
  `MONTH_SUPPORT_PARENTS` decimal(10, 2) NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TOTAL_GRYLJ` decimal(18, 2) NULL DEFAULT NULL,
  `YLJ_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个人养老金下载状态',
  `YLJ_UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '养老金更新时间',
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sub_tax_report_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_sub_tax_report_detail`;
CREATE TABLE `t_sub_tax_report_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REPORT_TOTAL_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATURAL_PERSON_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CURRENT_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_TAXFREE_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_TAXFREE_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_DEDUCTION_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_DEDUCTION_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `PENSION_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `MEDICAL_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `UNEMPLOYMENT_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `HOUSING_FUND` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_SPECIAL_DEDUCTION_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `PRE_CUR_SPECIAL_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `YEAR_MOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `BUSS_HEALTH_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_DEFER_PENSION_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `OTHER_MONTH` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_OTHER` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_CHILDREN_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_SUPPORT_PARENTS` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_HOME_LOADS` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_HOUSE_RENT` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_FURTHER_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_YYEZHFZC` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_SPECIAL_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_CHILDREN_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_SUPPORT_PARENTS` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_HOME_LOADS` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_HOUSE_RENT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_FURTHER_EDU` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_YYEZHFZC` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_SPECIAL_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_DONATE` decimal(10, 2) NULL DEFAULT NULL,
  `TAXABLE_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_RATE` decimal(10, 2) NULL DEFAULT NULL,
  `BASE_DEDUCT_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_TAXABLE_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_DEDUCTION_MOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_DEDUCTION_MOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_TAXABLE_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_TAX_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_TAX_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_TAX_REFUND` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_DEDUCTION_FEE` decimal(10, 2) NULL DEFAULT NULL,
  `REMARK` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_OTHER` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_DONATE` decimal(10, 2) NULL DEFAULT NULL,
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXHIBITION_COST` decimal(10, 2) NULL DEFAULT NULL,
  `CURRENT_DEDUCTIONITEMS_MOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `DATA_SOURCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `ACTUAL_DONATIONS` decimal(10, 2) NULL DEFAULT NULL,
  `REDUCE_RATIO` int(10) UNSIGNED NULL DEFAULT NULL,
  `DONATION_WAY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TOTAL_GRYLJ` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_REPORTTOTALID`(`REPORT_TOTAL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sub_tax_report_total
-- ----------------------------
DROP TABLE IF EXISTS `t_sub_tax_report_total`;
CREATE TABLE `t_sub_tax_report_total`  (
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_EMP_COUNTS` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_TOTAL_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `HIS_TOTAL_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `CALCULATE_TAX_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `REAL_TAX_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_DIFF_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `REPORT_ENABLED` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CANCEL_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_MONTH` date NULL DEFAULT NULL,
  `REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `SUB_TAX_REPORT_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SYSTEM_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_DATE` date NULL DEFAULT NULL,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sync_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_sync_employee`;
CREATE TABLE `t_sync_employee`  (
  `SYNC_EMP_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `DEAL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_HASH` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RET_CODE` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RET_INFO` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`SYNC_EMP_ID`) USING BTREE,
  INDEX `T_SYNC_EMPLOYEE_EMP_HASH_INDEX`(`EMP_HASH`) USING BTREE,
  INDEX `T_SYNC_EMPLOYEE_EMP_ID_INDEX`(`EMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOCKED_TIME` datetime NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_options
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_options`;
CREATE TABLE `t_sys_options`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `OPTION_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名',
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段编码',
  `RELATION_OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联字段编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_options_enum
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_options_enum`;
CREATE TABLE `t_sys_options_enum`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段编码',
  `OPTION_ENUM_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '枚举名称',
  `OPTION_ENUM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '枚举编码',
  `ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否启用',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排列序号',
  `RELATION_OPTION_ENUM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联枚举编码',
  `EDIT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否可编辑',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_template_field
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_template_field`;
CREATE TABLE `t_sys_template_field`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `FIELD_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名称',
  `FIELD_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段编码',
  `FIELD_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段类型',
  `GROUP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分组编码',
  `EMPLOYYE_DETAIL_PC` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '显示详情页（PC）',
  `EMPLOYEE_DETAIL_H5` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个人信息界面（移动端）',
  `EMPLOYEE_ENTRY_PC` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表（PC）',
  `EMPLOYEE_ENTRY_H5` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入职登记表（移动端）',
  `ADD_EMPLOYEE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '添加员工页',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排序序号',
  `OPTION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下拉选项',
  `FIELD_LENGTH` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '小数位数、字段长度',
  `FIELD_REMARK` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提示文案',
  `TIPS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'tips文案',
  `LABLE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分类编码',
  `MUILTPLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否支持多选',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_template_group
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_template_group`;
CREATE TABLE `t_sys_template_group`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `GROUP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组名称',
  `GROUP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组编码',
  `SEQ_NO` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '排序序号',
  `LABLE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分类编码',
  `LABLE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属分类名称',
  `ADD_MUILTPLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否支持多条录入',
  `EXPORT_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否可导出',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_template_lable
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_template_lable`;
CREATE TABLE `t_sys_template_lable`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LABLE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分类名称',
  `LABLE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分类编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_task
-- ----------------------------
DROP TABLE IF EXISTS `t_task`;
CREATE TABLE `t_task`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TASK_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务名',
  `ACCEPT_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TASK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CURRENT_MONTH` date NULL DEFAULT NULL,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `FAIL_REASON` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_dispute_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_dispute_detail`;
CREATE TABLE `t_tax_dispute_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MSG_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息唯一代码',
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '身份证号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `XXLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息类型',
  `ZYSSXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '争议申诉序号',
  `SSSX_DM` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉事项代码',
  `SSLX_MC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉类型名称',
  `XQFKRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '限期反馈日期',
  `LZRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职日期',
  `SSRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉日期',
  `BCSM` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '补充说明',
  `SFFK` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否反馈',
  `SSMXLB` varchar(7200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉明细列表',
  `DEAL_RESULT` varchar(7200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '争议申诉处理结果',
  `DEAL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理状态',
  `DEAL_DATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理日期',
  `FKRXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈人姓名',
  `FKRLXDH` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈人联系电话',
  `FKSM` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈说明',
  `FAIL_REASON` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '失败原因',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_dispute_detail_COMP_ID_TAX_SUB_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '争议申诉结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_invite_confirm_info
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_invite_confirm_info`;
CREATE TABLE `t_tax_invite_confirm_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '身份证号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `GJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍（地区）',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `DEAL_STATUS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理状态',
  `FEEDBACK_STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈状态',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `DEAL_DATE` date NULL DEFAULT NULL COMMENT '处理日期',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `FAIL_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈失败原因',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_invite_confirm_info_COMP_ID_TAX_SUB_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '邀请确认人员信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_item_config
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_item_config`;
CREATE TABLE `t_tax_item_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REPORT_ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ITEM_CODE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ITEM_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTENT` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIG_ENABLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_online_inquiry_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_online_inquiry_detail`;
CREATE TABLE `t_tax_online_inquiry_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MSG_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息唯一代码',
  `BT` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标题',
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '身份证号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '获取日期',
  `NSRDAH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人档案号',
  `RWXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务序号',
  `ZRRDAH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人档案号',
  `FXMXXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '风险明细序号',
  `XXLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息类型',
  `ZYSSXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '争议申诉序号',
  `SSSXMC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉事项名称',
  `SSLX_MC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉类型名称',
  `SSRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉日期',
  `LZRQ` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '离职日期',
  `BCSM` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '补充说明',
  `ZGSWRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务人员',
  `RWBJQX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务办结期限',
  `FXZT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '风险主题',
  `XQFKRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '限期反馈日期',
  `SSMXLB` varchar(7000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申诉明细列表',
  `DEAL_RESULT` varchar(7000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '线上询证处理结果',
  `DEAL_DATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理日期',
  `DEAL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理状态',
  `FKRXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈人姓名',
  `MOBILE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈人手机号码',
  `BZ` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注（反馈说明）',
  `FAIL_REASON` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '失败原因',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_online_inquiry_detail_COMP_ID_TAX_SUB_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE,
  INDEX `t_tax_online_inquiry_detail_MSG_ID_index`(`MSG_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '线上询证结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_payment_certificate_file
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_payment_certificate_file`;
CREATE TABLE `t_tax_payment_certificate_file`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TAX_PAYMENT_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '完税证明记录表ID',
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '月份',
  `FILE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ERROR_INFO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_TAX_PAYMENT_CERTIFICATE_FILE_RECORD_ID_index`(`TAX_PAYMENT_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_payment_certificate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_payment_certificate_record`;
CREATE TABLE `t_tax_payment_certificate_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `BBHZFH` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表汇总返回',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载信息',
  `DOWNLOAD_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载状态',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `REPORT_FROM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表名称',
  `SBRC` int(11) NULL DEFAULT NULL COMMENT '申报人次',
  `DQSR` decimal(32, 10) NULL DEFAULT NULL COMMENT '当期收入',
  `YTBSE` decimal(32, 10) NULL DEFAULT NULL COMMENT '应补退税额',
  `SBLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报类型',
  `REPORT_FROM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `SBQD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报渠道',
  `SBSJ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报时间',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_TAX_PAYMENT_CERTIFICATE_RECORD_COMP_ID_index`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_personal_report_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_personal_report_detail`;
CREATE TABLE `t_tax_personal_report_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '身份证号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '税款所属期',
  `FLSD_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分类所得数据',
  `YKYJ_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '综合所得数据',
  `DOWNLOAD_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载状态',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `FAIL_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈失败原因',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MONTH_EMP`(`TAX_SUB_ID`, `MONTH_DATE`, `ID_NO`) USING BTREE,
  INDEX `t_tax_personal_report_detail_COMP_ID_TAX_SUB_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人扣缴明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_rate_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_rate_rule`;
CREATE TABLE `t_tax_rate_rule`  (
  `TAX_TYPE` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RATE_LEVEL` int(10) UNSIGNED NULL DEFAULT NULL,
  `MININUM_AMOUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `MAXIMUM_AMOUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_RATE` decimal(4, 2) NULL DEFAULT NULL,
  `BASE_DEDUCT_AMOUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_refund_fee
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_refund_fee`;
CREATE TABLE `t_tax_refund_fee`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NOT NULL,
  `TAX_SUB_ID` int(11) NOT NULL,
  `APPLY_YEAR` date NULL DEFAULT NULL COMMENT '申报时间',
  `APPLY_AMOUNT` decimal(8, 2) NULL DEFAULT NULL COMMENT '申请手续费金额',
  `FORM_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结报单编号',
  `FORM_STATUS` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结报单状态',
  `FORM_STATUS_DETAIL` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结报单状态详情',
  `BANK_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行信息列表json',
  `SEHJ` decimal(8, 2) NULL DEFAULT NULL COMMENT '税额合计',
  `SJTJE` decimal(8, 2) UNSIGNED NULL DEFAULT NULL COMMENT '实缴金额合计',
  `PZSL` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '票证数量',
  `SXFJE` decimal(8, 2) NULL DEFAULT NULL COMMENT '申请手续费金额',
  `PZZLMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '票证名称',
  `ZSPMMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '品目名称',
  `ZSXMMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税种',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `SXFQCLB` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代扣代缴手续费申请表',
  `JBDHDHHDQC` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结报单和单户核对清册',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_refund_fee_COMP_ID_TAX_SUB_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '退税费用表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_refund_fee_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_refund_fee_detail`;
CREATE TABLE `t_tax_refund_fee_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(11) NULL DEFAULT NULL,
  `TAX_SUB_ID` int(11) NULL DEFAULT NULL,
  `REFUND_FEE_ID` int(11) NULL DEFAULT NULL,
  `FORM_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZSXMMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收项目名称',
  `ZSPMMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收品目名称',
  `DZSPHM` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子税票号码',
  `HIDEDZSPHM` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '隐藏电子税票号码',
  `YPZZLMC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '票证种类名称',
  `YPZZLDM` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '票证种类代码',
  `SJTJE` decimal(8, 2) NULL DEFAULT NULL COMMENT '实缴（退）金额',
  `RTKRQ` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入（退）库日期',
  `SKSSSWJGMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属税务机关名称',
  `ZGSWSKFJMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务所（科）分局名称',
  `TF` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '已退付',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_REFUND_DETAIL_ID_IDX`(`REFUND_FEE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '退税费用详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_report_form_file
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_report_form_file`;
CREATE TABLE `t_tax_report_form_file`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '月份',
  `REPORT_FORM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类型',
  `FILE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ERROR_INFO` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_report_form_file_TAX_SUB_ID_COMP_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '申报表下载数据项' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_report_info
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_report_info`;
CREATE TABLE `t_tax_report_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `REPORT_DAY` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `OPER_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORKER_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORKER_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_DAY` date NULL DEFAULT NULL,
  `LEAVE_DAY` date NULL DEFAULT NULL,
  `DEL_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_FINISH_TIME` timestamp NULL DEFAULT NULL,
  `ID_VALID_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATURAL_PERSON_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SYSTEM_REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ADD_MONTH` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETE_MONTH` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SW_WORKER_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SERVYOU_REPORT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SELF_REPORT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FAIL_RESON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `ACCOUNT_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCOUNT_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NATRUAL_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMP_SITUATION` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEDUCTION_COST_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DJXH_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_PERSON_REPORT_EMP`(`COMP_ID`, `EMP_ID`, `REPORT_STATUS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_report_record
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_report_record`;
CREATE TABLE `t_tax_report_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '税款所属期',
  `SBSJ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报时间',
  `CAPTION` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报记录标题',
  `DQSRE` decimal(32, 2) NULL DEFAULT NULL COMMENT '当期收入额',
  `SBRC` int(11) NULL DEFAULT NULL COMMENT '申报人次',
  `YBTSE` decimal(32, 2) NULL DEFAULT NULL COMMENT '应补退税额',
  `SBLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报类型',
  `SBZT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报状态',
  `REPORT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报类型（综合所得|分类所得）',
  `DOWNLOAD_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载状态',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `FAIL_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈失败原因',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_tax_report_record_TAX_SUB_ID_COMP_ID_index`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业申报记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_sub_auth_info
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_sub_auth_info`;
CREATE TABLE `t_tax_sub_auth_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '扣缴义务人id',
  `LEGAL_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `BUSINESS_LICENSE` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '营业执照',
  `LEGAL_ID_PHOTO` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人证件照',
  `APPLY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申请人姓名',
  `APPLY_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申请人手机号码',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_COMP_TAX`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_subject
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_subject`;
CREATE TABLE `t_tax_subject`  (
  `TAX_SUB_ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_SUB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEPT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_SUB_ENABLED` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXT1` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXT2` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `REMARK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DJXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPORT_PWD` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCREDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FAIL_REASON` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONFIRM_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTH_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `AREA_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMPLOYEE_ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `CONTRACT_ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `TAX_ENABLE_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0',
  `CONTRACT_AUTH_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTRACT_AUTH_FAIL_REASON` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合同认证驳回原因',
  `SW_ACCREDIT_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人姓名',
  `CONTACT_PHONE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人电话',
  `BANK_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTERED_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BMBH` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FACE_CHANNEL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FACE_OPERATE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CUSTOMER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ECIF` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CITIC_PAY_PROTOCOLS` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开薪易代发协议号',
  `CLIENT_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '核心客户号',
  `PROVINCE_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '省级编码',
  `PROVINCE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '省级名称',
  `CITY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '市级编码',
  `CITY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '市级名称',
  `COUNTY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '县级编码',
  `COUNTY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '县级名称',
  `TOWN_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '乡级编码',
  `TOWN_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '乡级名称',
  PRIMARY KEY (`TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_subject_seal
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_subject_seal`;
CREATE TABLE `t_tax_subject_seal`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `IMAGE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公章图片',
  `DEFAULT_IMG_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否为默认',
  `SIGN_IMAGE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签章类型 系统、手写',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_SUB_SEAL_COMP`(`COMP_ID`, `TAX_SUB_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_total_base
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_total_base`;
CREATE TABLE `t_tax_total_base`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `END_MONTH` date NULL DEFAULT NULL,
  `TOTAL_INCOME` decimal(10, 2) NULL DEFAULT NULL,
  `SPECIAL_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `OTHER_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_DONATED` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_BREAK_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_TOTAL` decimal(10, 2) NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `TAX_SUB_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `CURRENT_INCOME` decimal(10, 2) NULL DEFAULT NULL COMMENT '当期收入额',
  `CURRENT_TAXFREE_INCOME` decimal(10, 2) NULL DEFAULT NULL COMMENT '当期免税收入',
  `CURRENT_DONATE` decimal(10, 2) NULL DEFAULT NULL COMMENT '准予扣除捐赠额',
  `PENSION_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '基本养老保险',
  `MEDICAL_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '基本医疗保险',
  `UNEMPLOYMENT_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '失业保险',
  `HOUSING_FUND` decimal(10, 2) NULL DEFAULT NULL COMMENT '住房公积金',
  `YEAR_MOUNT` decimal(10, 2) NULL DEFAULT NULL COMMENT '年金',
  `BUSS_HEALTH_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '商业健康保险',
  `TAX_DEFER_PENSION_INSURANCE_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '税延养老保险',
  `OTHER_MONTH` decimal(10, 2) NULL DEFAULT NULL COMMENT '其他',
  `CURRENT_DEDUCTION_MOUNT` decimal(10, 2) NULL DEFAULT NULL COMMENT '本期减免税额',
  `TOTAL_TAXFREE_INCOME` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计免税收入',
  `TOTAL_DEDUCTION_FEE` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计减除费用',
  `TOTAL_CHILDREN_EDU` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计子女教育支出',
  `TOTAL_SUPPORT_PARENTS` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计赡养老人支出',
  `TOTAL_HOME_LOADS` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计住房贷款利息',
  `TOTAL_HOUSE_RENT` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计住房租金支出',
  `TOTAL_FURTHER_EDU` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计继续教育支出',
  `TOTAL_YYEZHFZC` decimal(10, 2) NULL DEFAULT NULL,
  `TOTAL_OTHER` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计其他扣除',
  `TOTAL_TAXABLE_INCOME` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计应纳税额',
  `TOTAL_TAXABLE_DEDUCTION` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计应扣缴税额',
  `CURRENT_TAX_REFUND` decimal(10, 2) NULL DEFAULT NULL COMMENT '应补(退)税额',
  `TAXABLE_INCOME` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计应纳税所得额',
  `TOTAL_GRYLJ` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_template
-- ----------------------------
DROP TABLE IF EXISTS `t_template`;
CREATE TABLE `t_template`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TEMPLATE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板名称',
  `TEMPLATE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板类型',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `TEMPLATE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板状态 启用、禁用、删除',
  `CREATER_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者姓名',
  `CREATER_USER_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建者用户UID',
  `TEMPLATE_USE_TIMES` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板使用次数',
  `SIGNING_TEMPLATE_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '云签模板编号',
  `EDIT_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板编辑url',
  `UPLOAD_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板上传url',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMPLATE_COMP_ID`(`COMP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_template_fileds
-- ----------------------------
DROP TABLE IF EXISTS `t_template_fileds`;
CREATE TABLE `t_template_fileds`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TEMPLATE_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板id',
  `TEMPLATE_STEP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板步骤id',
  `TEMPLATE_STEP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板步骤名',
  `FIELD_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段名',
  `RELATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项',
  `RELATION_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项名称',
  `RELATION_GROUP` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联项所属分组',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `OPERATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMP_FILEDS_STEP_ID`(`TEMPLATE_STEP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_template_flow_step
-- ----------------------------
DROP TABLE IF EXISTS `t_template_flow_step`;
CREATE TABLE `t_template_flow_step`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业id',
  `TEMPLATE_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板id',
  `OPERATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作类型 公章签署、个人签署、抄送',
  `COMP_EMP_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '企业员工id',
  `SORTBY` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '步骤排序',
  `STEP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '步骤名',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `EMP_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMP_STEP_TEMPID`(`TEMPLATE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_template_range
-- ----------------------------
DROP TABLE IF EXISTS `t_template_range`;
CREATE TABLE `t_template_range`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TEMPLATE_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '模板id',
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '公司id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_TEMP_RANGE_TEMPID`(`TEMPLATE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_triple_agreement
-- ----------------------------
DROP TABLE IF EXISTS `t_triple_agreement`;
CREATE TABLE `t_triple_agreement`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `TAX_SUB_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_UUID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_ORG_COED` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `YHHB_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `YHHB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `YHYYWD_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AREA_NUM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CLEARINGG_BANK_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCOUNT_BANK_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_ACCOUNT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_ACCOUNT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_STATUS_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_VERIFY_INFO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TRIPLE_AGREEMENT_PASS_DATE` date NULL DEFAULT NULL,
  `BATCH_PROXY_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DATA_SYNC_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `VALID_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_ORG_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ACTIVATE_FLAG` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  `AGREEMENT_DEFAULT` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_data_auth
-- ----------------------------
DROP TABLE IF EXISTS `t_user_data_auth`;
CREATE TABLE `t_user_data_auth`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `USER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTH_TYPE_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `OPERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `AUTH_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_AUTH_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_worker_type_config
-- ----------------------------
DROP TABLE IF EXISTS `t_worker_type_config`;
CREATE TABLE `t_worker_type_config`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `EMP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORKER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Foreign Keys structure for table qrtz_blob_triggers
-- ----------------------------
ALTER TABLE `qrtz_blob_triggers` ADD CONSTRAINT `qrtz_blob_triggers_OBFK_1744187710245028` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_cron_triggers
-- ----------------------------
ALTER TABLE `qrtz_cron_triggers` ADD CONSTRAINT `qrtz_cron_triggers_OBFK_1744187709977209` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_simple_triggers
-- ----------------------------
ALTER TABLE `qrtz_simple_triggers` ADD CONSTRAINT `qrtz_simple_triggers_OBFK_1744187709821814` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_simprop_triggers
-- ----------------------------
ALTER TABLE `qrtz_simprop_triggers` ADD CONSTRAINT `qrtz_simprop_triggers_OBFK_1744187710112107` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_triggers
-- ----------------------------
ALTER TABLE `qrtz_triggers` ADD CONSTRAINT `qrtz_triggers_OBFK_1744187709660641` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `qrtz_job_details` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

SET FOREIGN_KEY_CHECKS = 1;
