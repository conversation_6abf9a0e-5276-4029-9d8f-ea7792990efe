/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : merchant

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:26:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_access_token
-- ----------------------------
DROP TABLE IF EXISTS `t_access_token`;
CREATE TABLE `t_access_token`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APP_KEY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'APP_KEY',
  `APP_SECRET` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'APP密钥',
  `APP_STATUS` tinyint(4) NOT NULL COMMENT '是否启用',
  `DESC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `APP_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型',
  `CO_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '三方应用配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_acl
-- ----------------------------
DROP TABLE IF EXISTS `t_acl`;
CREATE TABLE `t_acl`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL COMMENT '删除',
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  `DISABLED` bit(1) NULL DEFAULT NULL COMMENT '禁用',
  `TENANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  `MULTIVERSE` int(11) NULL DEFAULT NULL COMMENT '维度',
  `PERMISSIONS` bigint(20) NULL DEFAULT NULL COMMENT '权限',
  `PRINCIPAL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主体',
  `PRINCIPAL_TYPE` int(11) NULL DEFAULT NULL COMMENT '主体类型',
  `SECURABLE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '安全对象',
  `SECURABLE_TYPE` int(11) NULL DEFAULT NULL COMMENT '安全对象类型',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ACL_MV_PCP`(`MULTIVERSE`, `PRINCIPAL_TYPE`, `PRINCIPAL`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '访问控制列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advert
-- ----------------------------
DROP TABLE IF EXISTS `t_advert`;
CREATE TABLE `t_advert`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ADVERT_POSITION_ID` bigint(20) NOT NULL COMMENT '广告位置id',
  `ADVERT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '广告名称',
  `REMARKS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '备注',
  `IS_USABLE` bit(1) NOT NULL COMMENT '是否可用',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ADVERT_IDX_ADVERT_NAME`(`ADVERT_NAME`) USING BTREE,
  INDEX `ADVERT_IDX_ADVERT_POSITION_ID`(`ADVERT_POSITION_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '广告' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advert_browse_record
-- ----------------------------
DROP TABLE IF EXISTS `t_advert_browse_record`;
CREATE TABLE `t_advert_browse_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ADVERT_CONTENT_ID` bigint(20) NOT NULL COMMENT '广告内容id',
  `USER_ID` bigint(20) NOT NULL COMMENT '用户id',
  `USER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户类型',
  `VISIT_IP` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '访问ip',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ADVERT_B_R_IDX_ADVERT_CONTENT_ID`(`ADVERT_CONTENT_ID`) USING BTREE,
  INDEX `ADVERT_B_R_IDX_USER_ID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '广告浏览记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advert_content
-- ----------------------------
DROP TABLE IF EXISTS `t_advert_content`;
CREATE TABLE `t_advert_content`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ADVERT_ID` bigint(20) NOT NULL COMMENT '广告id',
  `ADVERT_CONTENT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '广告内容名称',
  `JUMP_PICTURE_URL` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '跳转图片URL',
  `JUMP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '跳转类型',
  `JUMP_WEB_URL` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '跳转网页URL',
  `SHOW_PICTURE_URL` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '展示图片URL',
  `SEQUENCE` int(11) NOT NULL COMMENT '顺序',
  `BROWSE_NUMBER` bigint(20) NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `IS_USABLE` bit(1) NOT NULL COMMENT '是否可用',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ADVERT_C_IDX_ADVERT_CONTENT_NAME`(`ADVERT_CONTENT_NAME`) USING BTREE,
  INDEX `ADVERT_C_IDX_ADVERT_ID`(`ADVERT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '广告内容' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advert_position
-- ----------------------------
DROP TABLE IF EXISTS `t_advert_position`;
CREATE TABLE `t_advert_position`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `POSITION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '位置编码',
  `POSITION_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '位置名称',
  `TERMINAL_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '终端类型',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `POSITION_CODE`(`POSITION_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '广告位置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advise
-- ----------------------------
DROP TABLE IF EXISTS `t_advise`;
CREATE TABLE `t_advise`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TITLE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标题',
  `NOTICE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通知类型',
  `LINK` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '链接',
  `CONTENT` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '内容',
  `APPLICATION_SOURCE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发布应用来源',
  `IS_TOP` bit(1) NOT NULL COMMENT '置顶',
  `IS_POPUP` bit(1) NOT NULL COMMENT '强制弹框',
  `IS_VISIBLE` bit(1) NOT NULL COMMENT '是否可见',
  `CREATE_TIME` datetime(6) NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime(6) NOT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `TERMINAL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '终端',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通知' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advise_archive
-- ----------------------------
DROP TABLE IF EXISTS `t_advise_archive`;
CREATE TABLE `t_advise_archive`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NOTICE_ID` bigint(20) NOT NULL COMMENT '公告ID',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ARCHIVE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档名称',
  `CREATE_TIME` datetime(6) NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime(6) NOT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ADVISE_F_NID_AID`(`NOTICE_ID`, `ARCHIVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通知附件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_advise_user
-- ----------------------------
DROP TABLE IF EXISTS `t_advise_user`;
CREATE TABLE `t_advise_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NOTICE_ID` bigint(20) NOT NULL COMMENT '通知ID',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `IS_READ` bit(1) NOT NULL COMMENT '已读',
  `CREATE_TIME` datetime(6) NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime(6) NOT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `IS_DELETE` bit(1) NOT NULL COMMENT '已删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ADVISE_U_MID`(`MERCHANT_ID`) USING BTREE,
  INDEX `IDX_ADVISE_U_NID`(`NOTICE_ID`) USING BTREE,
  INDEX `IDX_ADVISE_U_UID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通知用户关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_agent_info
-- ----------------------------
DROP TABLE IF EXISTS `t_agent_info`;
CREATE TABLE `t_agent_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INTRODUCTION` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `IS_SHOW` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_application_group
-- ----------------------------
DROP TABLE IF EXISTS `t_application_group`;
CREATE TABLE `t_application_group`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `IS_ENABLE` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_application_group_app
-- ----------------------------
DROP TABLE IF EXISTS `t_application_group_app`;
CREATE TABLE `t_application_group_app`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APPLICATION_GROUP_ID` bigint(20) NOT NULL COMMENT '应用分组id',
  `APPLICATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用编码',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用组关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_application_map
-- ----------------------------
DROP TABLE IF EXISTS `t_application_map`;
CREATE TABLE `t_application_map`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MAP_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MAP_DETAIL` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用地图' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_area
-- ----------------------------
DROP TABLE IF EXISTS `t_area`;
CREATE TABLE `t_area`  (
  `AREA_ID` int(11) NOT NULL AUTO_INCREMENT,
  `AREA_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地区编码',
  `AREA_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地区名',
  `LEVEL` tinyint(4) NULL DEFAULT -1 COMMENT '地区级别（1:省份PROVINCE,2:市CITY,3:区县DISTRICT,4:街道STREET）',
  `CITY_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '城市编码',
  `CENTER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '城市中心点（即：经纬度坐标）',
  `PARENT_ID` int(11) NULL DEFAULT -1 COMMENT '地区父节点',
  PRIMARY KEY (`AREA_ID`) USING BTREE,
  INDEX `IDX_AREA_CODE`(`AREA_CODE`) USING BTREE,
  INDEX `IDX_AREA_NAME`(`AREA_NAME`) USING BTREE,
  INDEX `IDX_LEVEL`(`LEVEL`) USING BTREE,
  INDEX `IDX_PARENT_ID`(`PARENT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '区域' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_article
-- ----------------------------
DROP TABLE IF EXISTS `t_article`;
CREATE TABLE `t_article`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ARTICLE_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `TITLE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `DESCRIBE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ARTICLE_LINK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `SHOW_TIME` datetime NOT NULL,
  `SOURCE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COVER_IMAGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RELEASE_TIME` datetime NULL DEFAULT NULL,
  `EXPIRE_TIME` datetime NULL DEFAULT NULL,
  `IS_RELEASE` bit(1) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `UPDATE_TIME` datetime NOT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '文章' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank
-- ----------------------------
DROP TABLE IF EXISTS `t_bank`;
CREATE TABLE `t_bank`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `SWIFT_CODE` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'SWIFT银行代码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_account
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_account`;
CREATE TABLE `t_bank_account`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `ACCOUNT_BANK_NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户行名称',
  `BANK_BRANCH_ID` bigint(20) NULL DEFAULT NULL COMMENT '分行ID',
  `BANK_ID` bigint(20) NULL DEFAULT NULL COMMENT '银行ID',
  `IS_AUTH` bit(1) NULL DEFAULT NULL COMMENT '是否验证',
  `IS_INVOICE_ACCOUNT` bit(1) NULL DEFAULT NULL,
  `IS_TAX_ACCOUNT` bit(1) NULL DEFAULT NULL,
  `LG_BANK_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '暂时兼容灵工遗留数据的银行编码',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `NUMBER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编号',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行账户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_branch
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_branch`;
CREATE TABLE `t_bank_branch`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `BRANCH_LINK_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支行联行号',
  `BANK_LINK_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支行联行号',
  `BANK_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '银行',
  `BANK_ID` bigint(20) NULL DEFAULT NULL,
  `PROVINCE_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '省名称',
  `DISTRICT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '县市名称',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `uidx_branch_link_no`(`BRANCH_LINK_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '支行' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_card_bin
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_card_bin`;
CREATE TABLE `t_bank_card_bin`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `BANK_ID` bigint(20) NULL DEFAULT NULL,
  `BIN` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NUMBER_LENGTH` int(11) NULL DEFAULT NULL,
  `TYPE` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '卡BIN' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_branch_bank_info
-- ----------------------------
DROP TABLE IF EXISTS `t_branch_bank_info`;
CREATE TABLE `t_branch_bank_info`  (
  `BRANCH_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支行编码',
  `CNAPS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联行号',
  `BRANCH_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支行名称',
  `BANK_NAME_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行名称编码',
  `BANK_TEL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电话',
  PRIMARY KEY (`BRANCH_CODE`) USING BTREE,
  UNIQUE INDEX `BRANCH_NAME`(`BRANCH_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '支行信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_business
-- ----------------------------
DROP TABLE IF EXISTS `t_business`;
CREATE TABLE `t_business`  (
  `ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL COMMENT '是否禁用',
  `DISABLE_DISPLAY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `NAME`(`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_capital_account
-- ----------------------------
DROP TABLE IF EXISTS `t_capital_account`;
CREATE TABLE `t_capital_account`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `BHA_AUDIT_DESC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_TIME` datetime NULL DEFAULT NULL,
  `BHA_OPEN_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CAPITAL_ACCOUNT_MAIN_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CARD_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `MAIN_ID` bigint(20) NULL DEFAULT NULL,
  `PLATFORM_USER_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CAPITAL_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `CAPITAL_ACCOUNT_PLATFORM_USER_NO`(`PLATFORM_USER_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '资金账户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_certificate
-- ----------------------------
DROP TABLE IF EXISTS `t_certificate`;
CREATE TABLE `t_certificate`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `FILE_ID1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件',
  `FILE_ID2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件',
  `FILE_ID3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件',
  `FILE_ID4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件',
  `FILE_ID5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件',
  `ISSUED_BY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发证机构',
  `NUMBER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `TYPE` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件材料类型',
  `VALIDITY_BEGIN` datetime(6) NULL DEFAULT NULL COMMENT '有效期开始时间',
  `VALIDITY_END` datetime(6) NULL DEFAULT NULL COMMENT '有效期结束时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '证件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_city
-- ----------------------------
DROP TABLE IF EXISTS `t_city`;
CREATE TABLE `t_city`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `modify_time` datetime(6) NULL DEFAULT NULL,
  `version` int(11) NULL DEFAULT NULL,
  `country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `children_num` int(11) NULL DEFAULT NULL,
  `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `is_mainland` bit(1) NULL DEFAULT NULL,
  `deep` int(11) NULL DEFAULT NULL
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_city_bk
-- ----------------------------
DROP TABLE IF EXISTS `t_city_bk`;
CREATE TABLE `t_city_bk`  (
  `ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NULL DEFAULT NULL COMMENT '版本',
  `COUNTRY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国家',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `PARENT_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '上级城市',
  `CHILDREN_NUM` int(11) NULL DEFAULT NULL COMMENT '下级数量',
  `PATH` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '路径',
  `IS_MAINLAND` bit(1) NULL DEFAULT NULL COMMENT '是否是中国大陆地区',
  `DEEP` int(11) NULL DEFAULT NULL COMMENT '层次',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '城市' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_city_bk_0520
-- ----------------------------
DROP TABLE IF EXISTS `t_city_bk_0520`;
CREATE TABLE `t_city_bk_0520`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `modify_time` datetime(6) NULL DEFAULT NULL,
  `version` int(11) NULL DEFAULT NULL,
  `country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `children_num` int(11) NULL DEFAULT NULL,
  `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `is_mainland` bit(1) NULL DEFAULT NULL,
  `deep` int(11) NULL DEFAULT NULL
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_commodity
-- ----------------------------
DROP TABLE IF EXISTS `t_commodity`;
CREATE TABLE `t_commodity`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AMOUNT` decimal(19, 2) NULL DEFAULT NULL COMMENT '金额',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `DESCRIBE` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DETAIL_ID` bigint(20) NULL DEFAULT NULL COMMENT '详情id',
  `LAST_UP_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '类型',
  `UP_SHELF` bit(1) NULL DEFAULT NULL COMMENT '上架',
  `SORT` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `COMMODITY_DETAIL_ID`(`DETAIL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_config_definition`;
CREATE TABLE `t_config_definition`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'code',
  `config` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MERCHANT_ID`(`CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '配置定义' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config_info
-- ----------------------------
DROP TABLE IF EXISTS `t_config_info`;
CREATE TABLE `t_config_info`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业名称',
  `SLOGAN` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网站slogan',
  `DOMAIN_NAME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网站域名',
  `OLADING_ENTRANCE_ENABLE` tinyint(4) NOT NULL COMMENT '官网入口状态',
  `BOTTOM_DESCRIPTION` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网站底部其他说明',
  `ICP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `NETWORK_SECURITY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公安网备',
  `LOGO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网站Logo',
  `TAB_LOGO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网站标签页Logo',
  `CREATE_TIME` date NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` date NOT NULL COMMENT '最近更新时间',
  `IS_DELETE` tinyint(4) NOT NULL COMMENT '是否删除',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `AGREEMENT_NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户协议名称',
  `AGREEMENT_CONTENT` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '用户协议内容',
  `APPLET_QRCODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小程序二维码图片',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `DOMAIN_NAME`(`DOMAIN_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '配置信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_config_instance`;
CREATE TABLE `t_config_instance`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `DEFINITION_ID` bigint(20) NULL DEFAULT NULL COMMENT '配置定义ID',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `value` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '配置实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_consult_question
-- ----------------------------
DROP TABLE IF EXISTS `t_consult_question`;
CREATE TABLE `t_consult_question`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CONSULT_USER_ID` bigint(20) NOT NULL COMMENT '咨询人id',
  `USER_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户类型',
  `CONSULT_USER_MERCHANT_ID` bigint(20) NOT NULL COMMENT '咨询人商户id',
  `TITLE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `REPLY_STATE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '咨询状态',
  `CONSULT_WAY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '咨询方式',
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容',
  `QUESTION_TYPE_ID` bigint(20) NOT NULL COMMENT '问题类型id',
  `REPLY_NUMBER` bigint(20) NOT NULL COMMENT '回复数量',
  `IS_DELETE` bit(1) NOT NULL COMMENT '是否删除',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  `IS_READ` int(11) NULL DEFAULT 1 COMMENT '是否删除',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `QUESTION_IDX_CONSULT_USER_ID_MERCHANT_ID`(`CONSULT_USER_ID`, `CONSULT_USER_MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '专家问题' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_consult_question_reply
-- ----------------------------
DROP TABLE IF EXISTS `t_consult_question_reply`;
CREATE TABLE `t_consult_question_reply`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CONSULT_QUESTION_ID` bigint(20) NOT NULL COMMENT '咨询问题id',
  `REPLY_USER_ID` bigint(20) NOT NULL COMMENT '回复人id',
  `USER_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户类型',
  `REPLY_USER_MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '回复人企业id',
  `EXPERT_ID` bigint(20) NULL DEFAULT NULL COMMENT '专家id',
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容',
  `IS_DELETE` bit(1) NOT NULL COMMENT '是否删除',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `QUESTION_REPLY_IDX_CONSULT_QUESTION_ID`(`CONSULT_QUESTION_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '问题回复' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_consult_type
-- ----------------------------
DROP TABLE IF EXISTS `t_consult_type`;
CREATE TABLE `t_consult_type`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `IS_ENABLE` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否启用',
  `IS_DELETE` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '问题类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_consult_type_expert
-- ----------------------------
DROP TABLE IF EXISTS `t_consult_type_expert`;
CREATE TABLE `t_consult_type_expert`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CONSULT_TYPE_ID` bigint(20) NOT NULL COMMENT '咨询类型ID',
  `EXPERT_ID` bigint(20) NOT NULL COMMENT '专家ID',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `U_CONSULT_EXPERT_ID`(`EXPERT_ID`, `CONSULT_TYPE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '问题类型关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_country_region
-- ----------------------------
DROP TABLE IF EXISTS `t_country_region`;
CREATE TABLE `t_country_region`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `CODE2` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码2',
  `CODE3` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码3',
  `FULL_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '全称',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '简称',
  `ENGLISH_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '英语简称',
  `IS_SOVEREIGNTY` bit(1) NULL DEFAULT NULL COMMENT '是否独立主权',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '国家和地区' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_department
-- ----------------------------
DROP TABLE IF EXISTS `t_department`;
CREATE TABLE `t_department`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '部门名称',
  `SEQUENCE` int(11) NOT NULL COMMENT '顺序',
  `PARENT_ID` bigint(20) NOT NULL COMMENT '父部门id',
  `DEPARTMENT_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门类型',
  `PEOPLE_NUMBER` bigint(20) NOT NULL COMMENT '人数',
  `ID_LINK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门id链条',
  `NAME_LINK` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '部门名称链条',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  `DEPT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `DEPARTMENT_IDX_ID_LINK`(`ID_LINK`) USING BTREE,
  INDEX `DEPARTMENT_IDX_NAME_LINK`(`NAME_LINK`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '部门' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_department_personnel
-- ----------------------------
DROP TABLE IF EXISTS `t_department_personnel`;
CREATE TABLE `t_department_personnel`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DEPARTMENT_ID` bigint(20) NOT NULL COMMENT '部门ID',
  `PERSONNEL_ID` bigint(20) NOT NULL COMMENT '人员ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `DEPARTMENT_P_UK_DEPARTMENT_ID_PERSONNEL_ID`(`DEPARTMENT_ID`, `PERSONNEL_ID`) USING BTREE,
  INDEX `DEPARTMENT_P_IDX_DEPARTMENT_ID`(`DEPARTMENT_ID`) USING BTREE,
  INDEX `DEPARTMENT_P_IDX_PERSONNEL_ID`(`PERSONNEL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_department_principal
-- ----------------------------
DROP TABLE IF EXISTS `t_department_principal`;
CREATE TABLE `t_department_principal`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DEPARTMENT_ID` bigint(20) NOT NULL COMMENT '部门ID',
  `PRINCIPAL_PERSONNEL_ID` bigint(20) NOT NULL COMMENT '人员ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_DEPARTONNEL_ID`(`DEPARTMENT_ID`, `PRINCIPAL_PERSONNEL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_dept
-- ----------------------------
DROP TABLE IF EXISTS `t_dept`;
CREATE TABLE `t_dept`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DESCENDANT_MEMBER_COUNT` int(11) NULL DEFAULT NULL COMMENT '全成员数量',
  `MEMBER_COUNT` int(11) NULL DEFAULT NULL COMMENT '成员数量',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
  `PARENT_ID` bigint(20) NULL DEFAULT NULL COMMENT '父部门ID',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '顺序',
  `DING_DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '钉钉部门ID',
  `EXTERNAL_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部系统ID',
  `EFFECTIVE_TIME` datetime NULL DEFAULT NULL,
  `TYPE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '组织类型ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_DEPT_MID`(`MERCHANT_ID`) USING BTREE,
  INDEX `IDX_DEPT_PID`(`PARENT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '部门' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_dept_member
-- ----------------------------
DROP TABLE IF EXISTS `t_dept_member`;
CREATE TABLE `t_dept_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `IS_MANAGER` bit(1) NULL DEFAULT NULL COMMENT '是否管理员',
  `LEADER_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '直属上级ID',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `HIDDEN` bit(1) NULL DEFAULT NULL COMMENT '部门内不显示',
  `MERCHANT_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '人员ID',
  `TOP_TIME` datetime NULL DEFAULT NULL COMMENT '置顶时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_DEPT_M_DID`(`DEPT_ID`) USING BTREE,
  INDEX `IDX_DEPT_M_MID`(`MERCHANT_ID`) USING BTREE,
  INDEX `IDX_DEPT_M_UID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '部门成员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_dept_type
-- ----------------------------
DROP TABLE IF EXISTS `t_dept_type`;
CREATE TABLE `t_dept_type`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_employee
-- ----------------------------
DROP TABLE IF EXISTS `t_employee`;
CREATE TABLE `t_employee`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `EMPLOY_STATUS` int(11) NULL DEFAULT NULL COMMENT '入职状态',
  `IS_TRANSFERRED` bit(1) NULL DEFAULT NULL COMMENT '是否已转正',
  `LEGAL_ID` bigint(20) NULL DEFAULT NULL COMMENT '法人ID',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '人员ID',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '雇佣类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_expert
-- ----------------------------
DROP TABLE IF EXISTS `t_expert`;
CREATE TABLE `t_expert`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `HEAD_IMAGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '头像',
  `NICK_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '昵称',
  `USER_ID` bigint(20) NOT NULL COMMENT '用户ID',
  `VERSION` int(11) NOT NULL,
  `IS_DELETE` bit(1) NOT NULL COMMENT '删除',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '专家' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_external_user
-- ----------------------------
DROP TABLE IF EXISTS `t_external_user`;
CREATE TABLE `t_external_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `BIND_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '绑定ID',
  `TYPE` int(11) NULL DEFAULT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_file
-- ----------------------------
DROP TABLE IF EXISTS `t_file`;
CREATE TABLE `t_file`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `FILE_SIZE` bigint(20) NULL DEFAULT NULL COMMENT '文件大小',
  `IS_TEMP` bit(1) NULL DEFAULT NULL COMMENT '是否临时文件',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件完整名称',
  `OWNER_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '文件所有人',
  `UPLOAD_TIME` datetime(6) NULL DEFAULT NULL COMMENT '文件上传时间',
  `UPLOAD_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '文件上传人',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FILE_ARCHIVE_ID`(`ARCHIVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '文件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_file_log
-- ----------------------------
DROP TABLE IF EXISTS `t_file_log`;
CREATE TABLE `t_file_log`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_INIT_ID` bigint(20) NULL DEFAULT NULL,
  `DOWN_LOADER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DOWN_LOADER_ID` bigint(20) NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `DOWN_LOADER_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_form_field
-- ----------------------------
DROP TABLE IF EXISTS `t_form_field`;
CREATE TABLE `t_form_field`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `FIELD_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段属性',
  `TITLE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段属性名称',
  `CONTROL_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '控件类型',
  `FIELD_TYPE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字段属性类型',
  `IS_REQUIRED` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否必填',
  `IS_LOCK` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否锁定,不允许修改',
  `INSTRUCTION` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '说明',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_MODIFY_TIME` datetime NOT NULL COMMENT '最后修改时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_form_field_template
-- ----------------------------
DROP TABLE IF EXISTS `t_form_field_template`;
CREATE TABLE `t_form_field_template`  (
  `FIELD_ID` bigint(20) NOT NULL COMMENT '字段ID',
  `TEMPLATE_ID` bigint(20) NOT NULL COMMENT '模板ID',
  `IS_REQUIRED` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否必填',
  `IS_LOCK` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否锁定,不允许修改',
  `VERSION` int(11) NOT NULL,
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_MODIFY_TIME` datetime NOT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_FID`(`FIELD_ID`) USING BTREE,
  INDEX `IDX_TID`(`TEMPLATE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字段模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_form_template
-- ----------------------------
DROP TABLE IF EXISTS `t_form_template`;
CREATE TABLE `t_form_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表单模板名称',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_MODIFY_TIME` datetime NOT NULL COMMENT '最后修改时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_function
-- ----------------------------
DROP TABLE IF EXISTS `t_function`;
CREATE TABLE `t_function`  (
  `ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DEEP` int(11) NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `NAME_PATH` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PATH` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_holiday
-- ----------------------------
DROP TABLE IF EXISTS `t_holiday`;
CREATE TABLE `t_holiday`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `DATE_BEGIN` datetime(6) NULL DEFAULT NULL COMMENT '开始日期',
  `DATE_END` datetime(6) NULL DEFAULT NULL COMMENT '结束日期',
  `IS_DAY_OFF` bit(1) NULL DEFAULT NULL COMMENT '是否休息日',
  `IS_STATUTORY` bit(1) NULL DEFAULT NULL COMMENT '是否法定节假日',
  `NAME` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '节假日名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '节假日' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_increment_business
-- ----------------------------
DROP TABLE IF EXISTS `t_increment_business`;
CREATE TABLE `t_increment_business`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `INCREMENT_BUSINESS_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `INCREMENT_BUSINESS_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PREMIUM_DESC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REQUIRED` tinyint(1) NOT NULL DEFAULT 0,
  `BASIC_PAGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PREMIUM_PAGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETED` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '增值业务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_increment_business_merchant
-- ----------------------------
DROP TABLE IF EXISTS `t_increment_business_merchant`;
CREATE TABLE `t_increment_business_merchant`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `INCREMENT_BUSINESS_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `LEVEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'BASIC',
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '增值业务关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_inlet
-- ----------------------------
DROP TABLE IF EXISTS `t_inlet`;
CREATE TABLE `t_inlet`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ICON` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
  `BUSINESS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务编码',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `CODE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `PATH` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '路径',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '顺序',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `PROJECT_CODE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目编码',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '外部入口' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_legal
-- ----------------------------
DROP TABLE IF EXISTS `t_legal`;
CREATE TABLE `t_legal`  (
  `ID` bigint(20) NOT NULL COMMENT '法人实体ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `BUSINESS_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经营地址',
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子邮箱',
  `INTRO` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人实体简介',
  `INVOICE_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '默认开票账户ID',
  `IS_AUTH` bit(1) NULL DEFAULT NULL COMMENT '是否通过认证',
  `LEGAL_REPRESENTATIVE_ID` bigint(20) NULL DEFAULT NULL COMMENT '法定代表人个人信息ID',
  `LEGAL_USAGE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人实体用途',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '商户ID',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人实体名称',
  `POST_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮编',
  `REGISTERED_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册地址',
  `REGISTERED_CAPITAL` bigint(20) NULL DEFAULT NULL COMMENT '注册资本',
  `REGISTERED_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册电话',
  `SIMPLE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法人实体简称',
  `SOCIAL_CREDIT_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `STAFF_SIZE` int(11) NULL DEFAULT NULL COMMENT '人员规模',
  `TAX_NUMBER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '法人实体类型',
  `BUSINESS_LICENSE_ID` bigint(20) NULL DEFAULT NULL COMMENT '营业执照信息',
  `CONTACT_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人',
  `CONTACT_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系电话',
  `INDUSTRY` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '行业',
  `DEFAULT_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '默认对公账户ID',
  `TAX_REGISTRATION_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税务登记地址',
  `TAX_REGISTRATION_PHONE_NUMBER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税务登记电话',
  `TAX_REGISTRATION_BANK_ACCOUNT_NUMBER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税务登记开户行账户',
  `TAX_REGISTRATION_BANK_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税务登记开户行名称',
  `DEFAULT_SEAL_ID` int(11) NULL DEFAULT NULL COMMENT '默认电子印章',
  `CONTRACT_AUDIT_STATUS` int(11) NULL DEFAULT NULL COMMENT '合同主体审核状态',
  `STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_AUDIT_STATUS` int(11) NULL DEFAULT NULL,
  `TAX_AUDIT_TIME` datetime(6) NULL DEFAULT NULL,
  `TAX_DECLARATION_PASSWORD` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_OPERATOR_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FINANCE_COMMISSIONER` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '财务专员',
  `TAX_DECLARATION_COMMISSIONER` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个税申报专员',
  `IS_SUB_SECTOR` bit(1) NULL DEFAULT NULL COMMENT '纳税主体认证是否分部门',
  `UPDATER_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '法人' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_legal_audit_record
-- ----------------------------
DROP TABLE IF EXISTS `t_legal_audit_record`;
CREATE TABLE `t_legal_audit_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `AUDIT_STATUS` int(11) NULL DEFAULT NULL COMMENT '审核状态',
  `AUDIT_TIME` datetime(6) NULL DEFAULT NULL COMMENT '审核时间',
  `AUDITOR_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人名字',
  `AUDITOR_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '审核人用户ID',
  `ERROR_INFO` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核失败信息',
  `LEGAL_ID` bigint(20) NULL DEFAULT NULL COMMENT '法人实体ID',
  `LETTER_IMG_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '委托书',
  `SPONSOR_IDENTITY_TYPE` int(11) NULL DEFAULT NULL COMMENT '申请人身份类型',
  `SPONSOR_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '申请人用户ID',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '审核类型(合同审核)',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '法人审核记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_legal_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `t_legal_extend_info`;
CREATE TABLE `t_legal_extend_info`  (
  `ID` bigint(20) NOT NULL COMMENT '法人实体ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CGB_OFFICE_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '行所号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '法人扩展信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_member_activate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_member_activate_record`;
CREATE TABLE `t_member_activate_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ACTIVATION_ID` bigint(20) NULL DEFAULT NULL COMMENT '人员激活ID(席位ID）',
  `SOURCE_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '原成员ID',
  `TARGET_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '现成员ID',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '成员激活记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_member_activation
-- ----------------------------
DROP TABLE IF EXISTS `t_member_activation`;
CREATE TABLE `t_member_activation`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `LATEST_ACTIVATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近激活时间',
  `MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '绑定的人员ID',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `PREV_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '最近激活人员',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MEMBER_ACTIVATION_MEMBER_ID`(`MEMBER_ID`) USING BTREE,
  INDEX `IDX_MEMBER_ACTIVATION_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '成员激活' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant`;
CREATE TABLE `t_merchant`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AUDIT_DESC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核描述',
  `AUDIT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核状态',
  `AUDIT_TIME` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `BANK_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行名称',
  `BANK_NO` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户银行',
  `BUSINESS_LICENSE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '营业执照编号',
  `BUSINESS_LICENSE_IMG_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '营业执照图片',
  `CONTACT_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址',
  `CONTACTS` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人',
  `CONTACTS_EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系邮箱',
  `CONTACTS_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系手机',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `ID_CARD_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `IS_FORBIDDEN` bit(1) NULL DEFAULT NULL COMMENT '是否禁用',
  `LEGAL` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法定代表人',
  `LEGAL_ID_CARD_FRONT_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法定代表人证件',
  `LEGAL_ID_CARD_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法定代表人身份证',
  `LEGAL_ID_CARD_OPPOSITE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法定代表人身份证反面',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `ORG_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '组织机构号',
  `PRE_PAID_ACCOUNT_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROXY_LETTER_IMG_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册人',
  `REGISTER_CONTACTS_EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册联系邮箱',
  `REGISTER_CONTACTS_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册人手机',
  `REGISTER_ID_CARD_FRONT_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册人身份证',
  `REGISTER_ID_CARD_OPPOSITE_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENTRUSTED_BRANCH_BANK_INFO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_LICENSE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行开户许可证',
  `FEE_RATE` decimal(7, 5) NULL DEFAULT NULL,
  `PLATFORM_USER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UNIFIED_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `BHA_OPEN_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_DESC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_TIME` datetime NULL DEFAULT NULL,
  `TIEQI_USERNAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INTRODUCTION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '简介',
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  `REGISTER_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册地址',
  `POSTCODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮编',
  `ORG_NO_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_NO_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAXPAYER_IDENTIFICATION_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开票地址',
  `INVOICE_MOBILE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开票电话',
  `INVOICE_BANK_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开票银行',
  `INVOICE_BANK_NO` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_PROVINCE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CITY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENTRUSTED_BRANCH_BANK_INFO_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_ID_CARD_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_ID_CARD_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGN_BD` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AGENT_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WITHDRAW_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'T1',
  `ENABLE_WITHDRAW` int(11) NULL DEFAULT 0,
  `LOGO_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENABLE_TRANSFER_RECHARGE` int(11) NULL DEFAULT 1,
  `INDUSTRY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '行业',
  `ENABLE_DEFAULT_PROTOCOL` int(11) NULL DEFAULT NULL,
  `FIRST_CATEGORY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SECOND_CATEGORY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXPRESS_ADDRESS` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXPRESS_CONTACT` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXPRESS_CONTACT_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_LICENSE_IMG_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHANNEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注册渠道',
  `RELATED_BUSINESS` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTHORIZATION_CERTIFICATE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PLATFROM_LOGO_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEOVO_BAIYING_PERMANENT_CODE` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CORPID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RECOMMEND_CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NODE_ID` bigint(20) NULL DEFAULT NULL,
  `CREATOR_ID` bigint(20) NULL DEFAULT NULL,
  `NODE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_CONTROL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEFAULT_LEGAL_ID` bigint(20) NULL DEFAULT NULL,
  `STAGE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CGB_ECIF` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CODE` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '企业客户号',
  `EXTERNAL_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_REGION_AREA_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '登记机关代码/区县代码',
  `ACCOUNT_MANAGER_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '管户人编号（也是小鱼泡泡工号）',
  `ACCOUNT_MANAGER_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '管户人姓名',
  `ACCOUNT_MANAGER_ORG_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '管户机构编码',
  `ACCOUNT_MANAGER_ORG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '管户机构名称',
  `TOWN_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '乡级名称',
  `REASON` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '驳回原因',
  `TOWN_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '乡级编码',
  `COUNTY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '县级名称',
  `COUNTY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '县级编码',
  `CITY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '市级名称',
  `CITY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '市级编码',
  `PROVINCE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '省级名称',
  `PROVINCE_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '省级编码',
  `ADMIN_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '超管用户ID',
  `BLNG_INST_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BLNG_INSI` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIN_INST_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BIN_INSI` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TYY_CUST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `t_merchant_external_id_idx`(`EXTERNAL_ID`) USING BTREE,
  INDEX `t_merchant_MODIFY_TIME_IDX`(`MODIFY_TIME`, `CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_activation_summary
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_activation_summary`;
CREATE TABLE `t_merchant_activation_summary`  (
  `ID` bigint(20) NOT NULL COMMENT '企业ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CAPACITY` bigint(20) NULL DEFAULT NULL COMMENT '当前用户容量上限',
  `USED` bigint(20) NULL DEFAULT NULL COMMENT '已用容量',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业成员激活汇总' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_app
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_app`;
CREATE TABLE `t_merchant_app`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `COMPATIBILITY` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `DEMO_VIDEO` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DESCRIBE` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXP_LINK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOGO` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NOTE` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_RESULT_LINK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UP_SHELF` bit(1) NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `INTRO` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SERVICE_USE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `SERVICE_URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_SELF_APPLICATION` int(11) NULL DEFAULT 0,
  `MOBILE_ICON` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DISPLAY_TERMINAL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_NOT_OPEN_DISPLAY_RULE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'ADMIN_DISPLAY',
  `SEQ` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CODE`(`CODE`) USING BTREE,
  INDEX `MERCHANT_APP_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_app_meal
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_app_meal`;
CREATE TABLE `t_merchant_app_meal`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COMMODITY_ID` bigint(20) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `CYCLE` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LAST_UP_TIME` datetime NULL DEFAULT NULL,
  `MERCHANT_APP_ID` bigint(20) NULL DEFAULT NULL,
  `OPEN_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROTOCOL_MODE` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROTOCOL_TEMPLATE_ID` bigint(20) NULL DEFAULT NULL,
  `SIGNER_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIZE` int(11) NULL DEFAULT NULL,
  `SIZE_UNIT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REPEAT_PAY` bit(1) NULL DEFAULT NULL,
  `SERVICE_USE` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_MERCHANT` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `MEAL_COMMODITY_ID`(`COMMODITY_ID`) USING BTREE,
  INDEX `MEAL_MERCHANT_APP_ID`(`MERCHANT_APP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用套餐' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_app_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_app_menu`;
CREATE TABLE `t_merchant_app_menu`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `LEVEL` int(11) NOT NULL,
  `P_MENU_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MENU_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PRIVILEGE_GROUP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ROUTE_PATH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BELONG_PROJECT` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `INCREMENT_BUSINESS_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INCREMENT_LEVEL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  `PRODUCT_LINE_ID` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用菜单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_app_open_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_app_open_instance`;
CREATE TABLE `t_merchant_app_open_instance`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `MERCHANT_APP_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `OPEN_INSTANCE_ID`(`MERCHANT_ID`, `MERCHANT_APP_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用开关' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_business_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_business_instance`;
CREATE TABLE `t_merchant_business_instance`  (
  `ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `FEE_RATE` decimal(19, 4) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MERCHANT_BUSINESS_INSTANCE_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_business_rel
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_business_rel`;
CREATE TABLE `t_merchant_business_rel`  (
  `MERCHANT_ID` bigint(20) NOT NULL,
  `BUSINESS_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`MERCHANT_ID`, `BUSINESS_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_change_admin_request
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_change_admin_request`;
CREATE TABLE `t_merchant_change_admin_request`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CELL_PHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUDIT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BEFORE_CHANGE_PERSONNEL_ID` bigint(20) NULL DEFAULT NULL,
  `AFTER_CHANGE_PERSONNEL_ID` bigint(20) NULL DEFAULT NULL,
  `BEFORE_CHANGE_USER_ID` bigint(20) NULL DEFAULT NULL,
  `AFTER_CHANGE_USER_ID` bigint(20) NULL DEFAULT NULL,
  `BEFORE_MANAGER_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AFTER_MANAGER_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUTHORIZATION_CERTIFICATED_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业变更请求' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_change_record
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_change_record`;
CREATE TABLE `t_merchant_change_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DELETE_REASON` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业变更记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_config
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_config`;
CREATE TABLE `t_merchant_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `VALUE` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_customer
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_customer`;
CREATE TABLE `t_merchant_customer`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CUSTOMER_NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客户名称',
  `UNIFIED_CODE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '社会统一信用代码',
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  `CONTACTS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人',
  `CONTACTS_MOBILE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人电话',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `CUSTOMER_MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '客户企业ID',
  `CUSTOMER_STATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `CUSTOMER_AUDIT_STATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '认证状态',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `CUSTOMER_IDX_UNIFIED_CODE`(`UNIFIED_CODE`, `MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业客户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_init
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_init`;
CREATE TABLE `t_merchant_init`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `CONFIGURE_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SUBMITTER_USER_ID` bigint(20) NULL DEFAULT NULL,
  `SUBMITTER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SUBMITTER_PHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SUBMITTER_TIME` datetime NULL DEFAULT NULL,
  `OPERATOR` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OPERATOR_TIME` datetime NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `APP_CODES` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FILE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `MERCHANT_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_EMAIL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业初始化' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_inline_config
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_inline_config`;
CREATE TABLE `t_merchant_inline_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `FIELD_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `SHOW_FLAG` int(11) NULL DEFAULT NULL,
  `MUST_FLAG` int(11) NULL DEFAULT NULL,
  `REMARK` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `uni_filed_name`(`FIELD_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业初始化配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_lead
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_lead`;
CREATE TABLE `t_merchant_lead`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_member
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_member`;
CREATE TABLE `t_merchant_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员名称',
  `USER_ID` bigint(20) NOT NULL COMMENT '用户ID',
  `SEX` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '性别',
  `IDENTIFY_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  `IDENTIFY_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号',
  `CELL_PHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `HAVE_ENABLED` int(11) NULL DEFAULT NULL COMMENT '是否启用',
  `BIRTH_TIME` datetime NULL DEFAULT NULL COMMENT '生日',
  `ENTRY_TIME` datetime NULL DEFAULT NULL COMMENT '入职日期',
  `FIRST_WORK_TIME` datetime NULL DEFAULT NULL COMMENT '参加工作日期',
  `CONFIRMATION_TIME` datetime NULL DEFAULT NULL COMMENT '确认时间',
  `IS_DELETE` bit(1) NULL DEFAULT NULL COMMENT '是否删除',
  `DELETED` bit(1) NULL DEFAULT NULL COMMENT '是否删除',
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  `DISABLED` bit(1) NULL DEFAULT NULL COMMENT '是否禁用',
  `TENANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '租户',
  `JOB_NUMBER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工号',
  `PERSONAL_ID` bigint(20) NULL DEFAULT NULL COMMENT '个人信息ID',
  `CAPACITY_ACTIVATED_TIME` datetime NULL DEFAULT NULL COMMENT '席位激活时间',
  `IS_CAPACITY_ACTIVATED` bit(1) NULL DEFAULT NULL COMMENT '席位是否已激活',
  `EXTERNAL_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部系统成员ID',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_MERCHANT_M_MID_UID`(`MERCHANT_ID`, `USER_ID`) USING BTREE,
  INDEX `IDX_MERCHANT_M_MID`(`MERCHANT_ID`) USING BTREE,
  INDEX `t_merchant_member_user_id_idx`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业人员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_notice
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_notice`;
CREATE TABLE `t_merchant_notice`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TITLE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标题',
  `NOTICE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告类型',
  `CONTENT` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告内容',
  `APPLICATION_SOURCE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用来源',
  `ACCEPT_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '接收人类型',
  `IS_TOP` bit(1) NOT NULL COMMENT '置顶',
  `IS_EJECT` bit(1) NOT NULL COMMENT '弹框',
  `STATE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业通知' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_notice_archive
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_notice_archive`;
CREATE TABLE `t_merchant_notice_archive`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NOTICE_ID` bigint(20) NOT NULL COMMENT '公告ID',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `ARCHIVE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档名称',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `NOTICE_ARCHIVE_IDX_NOTICE_ID`(`NOTICE_ID`, `ARCHIVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业通知档案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_notice_user
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_notice_user`;
CREATE TABLE `t_merchant_notice_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NOTICE_ID` bigint(20) NOT NULL COMMENT '公告ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `USER_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户类型',
  `STATE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `IS_DELETE` bit(1) NULL DEFAULT NULL COMMENT '是否删除',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `NOTICE_USER_IDX_NOTICE_ID`(`NOTICE_ID`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业通知用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_personnel_config_template
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_personnel_config_template`;
CREATE TABLE `t_merchant_personnel_config_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业id',
  `IS_CAN_SELF_UPDATE_INFO` bit(1) NOT NULL COMMENT '是否能自己修改人员信\n息',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_personnel_field_template
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_personnel_field_template`;
CREATE TABLE `t_merchant_personnel_field_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业id',
  `PERSONNEL_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '人员类型',
  `FIELDS_DEFINE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段定义',
  `IS_DELETE` bit(1) NOT NULL COMMENT '是否删除',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `FIELD_TEMPLATE_IDX_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_user
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_user`;
CREATE TABLE `t_merchant_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CELL_PHONE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOGIN_ERROR_TIMES` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NEED_RESET_PASSWORD` bit(1) NULL DEFAULT NULL,
  `PASSWORD` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_TIME` datetime NULL DEFAULT NULL,
  `USER_SIGN_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BELONG_ENTERPRISE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEFAULT_PASSWORD` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '记录初始密码明文',
  `MERCHANT_USER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PERSON_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HAVE_ENABLED` int(11) NULL DEFAULT 1,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `HIDDEN_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '操作员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_user_role
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_user_role`;
CREATE TABLE `t_merchant_user_role`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_USER_ID` bigint(20) NOT NULL COMMENT '操作员ID',
  `ROLE_ID` bigint(20) NOT NULL COMMENT '角色id',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `MERCHANT_USER_ROLE_UK_MERCHANT_USER_ID_ROLE_ID`(`MERCHANT_USER_ID`, `ROLE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '操作员角色关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_msg`;
CREATE TABLE `t_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CONTENT` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息内容',
  `MSG_SOURCE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息来源类型',
  `TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标题',
  `URL` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '跳转地址',
  `FLOW_NO` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流水号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '消息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_msg_template
-- ----------------------------
DROP TABLE IF EXISTS `t_msg_template`;
CREATE TABLE `t_msg_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `APP_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '所属应用code',
  `CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '所属企业',
  `MERCHANT_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '创建人/更新人',
  `MSG_BUSINESS_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '所属消息业务code',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模板名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '消息模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_msg_template_info
-- ----------------------------
DROP TABLE IF EXISTS `t_msg_template_info`;
CREATE TABLE `t_msg_template_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CHANNEL_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '通道CODE',
  `DESCRIPTION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板内容描述',
  `SUB_TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '副标题',
  `TEMPLATE_ID` bigint(20) NOT NULL COMMENT '模版ID',
  `TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模版标题',
  `URL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模版跳转地址',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '消息模板信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_msg_user
-- ----------------------------
DROP TABLE IF EXISTS `t_msg_user`;
CREATE TABLE `t_msg_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `IS_READ` bit(1) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `MSG_CHANNEL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息通道',
  `MSG_ID` bigint(20) NULL DEFAULT NULL COMMENT '消息ID',
  `RECEIVE_MSG_TIME` datetime(6) NULL DEFAULT NULL COMMENT '接收/已读消息时间',
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '消息发送状态',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '消息用户关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nation
-- ----------------------------
DROP TABLE IF EXISTS `t_nation`;
CREATE TABLE `t_nation`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '民族' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_app_default_icon
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_app_default_icon`;
CREATE TABLE `t_nav_app_default_icon`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SCHEME_ID` bigint(20) NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ICON` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_app_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_app_menu`;
CREATE TABLE `t_nav_app_menu`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SCHEME_ID` bigint(20) NOT NULL,
  `P_ID` bigint(20) NULL DEFAULT NULL,
  `LEVEL` int(11) NOT NULL,
  `ORDER` int(11) NOT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MERCHANT_APP_MENU_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ICON` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NOT_OPEN_RULE` tinyint(1) NOT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_platform_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_platform_menu`;
CREATE TABLE `t_nav_platform_menu`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SCHEME_ID` bigint(20) NOT NULL,
  `P_ID` bigint(20) NULL DEFAULT NULL,
  `LEVEL` int(11) NOT NULL,
  `ORDER` int(11) NOT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `GROUP_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHECK_EVENT` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APP_MENU_ID` bigint(20) NULL DEFAULT NULL,
  `OPEN_URL` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `OPEN_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ICON` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_scheme
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_scheme`;
CREATE TABLE `t_nav_scheme`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATER` bigint(20) NOT NULL,
  `IS_DEFAULT` tinyint(1) NOT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_scheme_default_page
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_scheme_default_page`;
CREATE TABLE `t_nav_scheme_default_page`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SCHEME_ID` bigint(20) NOT NULL,
  `APP_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `DEFAULT_PAGE_OPEN` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEFAULT_PAGE_NOT_OPEN` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_nav_scheme_merchant
-- ----------------------------
DROP TABLE IF EXISTS `t_nav_scheme_merchant`;
CREATE TABLE `t_nav_scheme_merchant`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SCHEME_ID` bigint(20) NOT NULL,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `DELETED` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `t_operation_log`;
CREATE TABLE `t_operation_log`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `CONTAINER_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '容器名称',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `USERNAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `OPERATION_MENUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单',
  `OPERATION_TYPES` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `METHOD_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口类型（GET,POST,PUT,DELETE,OPTION)',
  `URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `PAYLOAD` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `IP` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP',
  `OPERATE_AT` datetime NULL DEFAULT NULL COMMENT '时间',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_org_position
-- ----------------------------
DROP TABLE IF EXISTS `t_org_position`;
CREATE TABLE `t_org_position`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '序号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_org_rank
-- ----------------------------
DROP TABLE IF EXISTS `t_org_rank`;
CREATE TABLE `t_org_rank`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `PARENT_ID` bigint(20) NOT NULL COMMENT '父职级ID',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '显示顺序',
  `WAY_ID` bigint(20) NOT NULL COMMENT '职级通道ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_org_rank_member
-- ----------------------------
DROP TABLE IF EXISTS `t_org_rank_member`;
CREATE TABLE `t_org_rank_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `RANK_ID` bigint(20) NOT NULL COMMENT '职级ID',
  `USER_ID` bigint(20) NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_org_rank_way
-- ----------------------------
DROP TABLE IF EXISTS `t_org_rank_way`;
CREATE TABLE `t_org_rank_way`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '顺序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_organization
-- ----------------------------
DROP TABLE IF EXISTS `t_organization`;
CREATE TABLE `t_organization`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代码',
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '商户id',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_organization_member
-- ----------------------------
DROP TABLE IF EXISTS `t_organization_member`;
CREATE TABLE `t_organization_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `IS_HIDDEN` bit(1) NULL DEFAULT NULL COMMENT '是否隐藏',
  `IS_MANAGER` bit(1) NULL DEFAULT NULL COMMENT '是否本组织主管',
  `LEADER_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '汇报上级成员ID',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '商户id',
  `MERCHANT_MEMBER_ID` bigint(20) NULL DEFAULT NULL COMMENT '人员ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '成员名称',
  `NODE_ID` bigint(20) NULL DEFAULT NULL COMMENT '组织节点',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_organization_node
-- ----------------------------
DROP TABLE IF EXISTS `t_organization_node`;
CREATE TABLE `t_organization_node`  (
  `ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `DESCENDANT_MEMBER_COUNT` int(11) NULL DEFAULT NULL COMMENT '本级以下成员数',
  `MEMBER_COUNT` int(11) NULL DEFAULT NULL COMMENT '本级成员数',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '商户id',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `ORG_ID` bigint(20) NULL DEFAULT NULL COMMENT '虚拟组织ID',
  `PARENT_ID` bigint(20) NULL DEFAULT NULL COMMENT '父组织ID',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_password
-- ----------------------------
DROP TABLE IF EXISTS `t_password`;
CREATE TABLE `t_password`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `ALGORITHM` int(11) NULL DEFAULT NULL COMMENT '加密算法',
  `CIPHERTEXT` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '加密后的密文',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '密码类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_commodity_order
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_commodity_order`;
CREATE TABLE `t_pay_commodity_order`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AMOUNT` decimal(19, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `ERROR` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `EXPIRY_TIME` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `LAST_UP_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `PAY_CHANNEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支付渠道',
  `PAYORDER_ID` bigint(20) NULL DEFAULT NULL COMMENT '支付订单id',
  `PAY_TIME` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `REQUEST_NO` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '流水号',
  `SHOPPER_ID` bigint(20) NULL DEFAULT NULL COMMENT '顾客id',
  `SHOPPER_MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '顾客企业',
  `STATUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `TARGET_STATUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目标状态',
  `USED` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否在用',
  `PAY_REQUESTNO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支付订单号',
  `PAY_COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '支付完成时间',
  `SHOPPER_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '顾客类型',
  `TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `REQUEST_NO`(`REQUEST_NO`) USING BTREE,
  INDEX `PAY_COMMODITY_ORDER_PAYORDER_ID`(`PAYORDER_ID`) USING BTREE,
  INDEX `PAY_COMMODITY_ORDER_SHOPPER_ID`(`SHOPPER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_commodity_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_commodity_order_detail`;
CREATE TABLE `t_pay_commodity_order_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ACTUAL_AMOUNT` decimal(19, 2) NULL DEFAULT NULL COMMENT '实际支付金额',
  `ACTUAL_REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `COMMODITY_ID` bigint(20) NULL DEFAULT NULL COMMENT '商品id',
  `ORDER_ID` bigint(20) NULL DEFAULT NULL COMMENT '订单id',
  `PROTOCOL_MODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '签约方式',
  `PROTOCOL_TEMPLATE_ID` bigint(20) NULL DEFAULT NULL COMMENT '协议模板id',
  `SELLER_ID` bigint(20) NULL DEFAULT NULL COMMENT '卖方id',
  `SPECS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '套餐',
  `TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品类型',
  `SIGN_ORDER_ID` bigint(20) NULL DEFAULT NULL COMMENT '协议订单id',
  `SIGN_FILE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `APP_INFO` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '信息快照',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `PAY_COMMODITY_ORDER_ID`(`ORDER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_order
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_order`;
CREATE TABLE `t_pay_order`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AMOUNT` decimal(19, 2) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `ERROR` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LAST_UP_TIME` datetime NULL DEFAULT NULL,
  `PAY_ACCOUNT` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_CHANNEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_COMMODITY_ORDER_ID` bigint(20) NULL DEFAULT NULL,
  `PAY_REQUEST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_TIME` datetime NULL DEFAULT NULL,
  `REQUEST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TARGET_STATUS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAY_COMPLETE_TIME` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `REQUEST_NO`(`REQUEST_NO`) USING BTREE,
  INDEX `PAY_ORDER_PAY_COMMODITY_ORDER_ID`(`PAY_COMMODITY_ORDER_ID`) USING BTREE,
  INDEX `PAY_ORDER_PAY_REQUEST_NO`(`PAY_REQUEST_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personal
-- ----------------------------
DROP TABLE IF EXISTS `t_personal`;
CREATE TABLE `t_personal`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '个人信息ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `BIRTHDAY` datetime(6) NULL DEFAULT NULL COMMENT '出生日期',
  `CELLPHONE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `CERTIFICATE_ID` bigint(20) NULL DEFAULT NULL COMMENT '默认证件ID',
  `CONTRACT_PHONE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系电话',
  `COUNTRY` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国家或者地区',
  `DOMICILE_PLACE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍所在地',
  `GENDER` int(11) NULL DEFAULT NULL COMMENT '性别',
  `HOME_ADDRESS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家庭现住址',
  `HOME_CITY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家庭现住址所在城市ID',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `NATION` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '民族',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_PERSONAL_OWNER`(`OWNER_TYPE`, `OWNER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personnel_form_json
-- ----------------------------
DROP TABLE IF EXISTS `t_personnel_form_json`;
CREATE TABLE `t_personnel_form_json`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `JSON_CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PERSONNEL_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personnel_info
-- ----------------------------
DROP TABLE IF EXISTS `t_personnel_info`;
CREATE TABLE `t_personnel_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CELL_PHONE` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `PERSONNEL_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `IS_CAN_SELF_UPDATE_INFO` bit(1) NOT NULL DEFAULT b'0',
  `REMARKS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_DELETE` bit(1) NOT NULL DEFAULT b'0',
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `STAFF_STATUS_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `PERSONNEL_INFO_IDX_CELL_PHONE_MERCHANT_ID`(`CELL_PHONE`, `MERCHANT_ID`) USING BTREE,
  INDEX `PERSONNEL_INFO_IDX_USER_ID_MERCHANT_ID`(`USER_ID`, `MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personnel_info_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_personnel_info_detail`;
CREATE TABLE `t_personnel_info_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PERSONNEL_INFO_ID` bigint(20) NOT NULL,
  `EXTEND_INFO` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `CONTRACT_EXPIRATION_TIME` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PERSONNEL_INFO_ID`(`PERSONNEL_INFO_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_personnel_merchant_user
-- ----------------------------
DROP TABLE IF EXISTS `t_personnel_merchant_user`;
CREATE TABLE `t_personnel_merchant_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PERSONNEL_ID` bigint(20) NOT NULL,
  `MERCHANT_USER_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NOT NULL,
  `LAST_UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PERSONNEL_MERCHANT_USER_UK_PERSONNEL_ID_MERCHANT_USER_ID`(`PERSONNEL_ID`, `MERCHANT_USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_post
-- ----------------------------
DROP TABLE IF EXISTS `t_post`;
CREATE TABLE `t_post`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `DESCRIPTION` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `POSITION_ID` bigint(20) NULL DEFAULT NULL COMMENT '职位ID',
  `QUANTITY` bigint(20) NULL DEFAULT NULL COMMENT '编制数量',
  `RANK_ID` bigint(20) NULL DEFAULT NULL COMMENT '职级ID',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_MERCHANT_ID_NAME`(`MERCHANT_ID`, `NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '岗位' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_post_member
-- ----------------------------
DROP TABLE IF EXISTS `t_post_member`;
CREATE TABLE `t_post_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `POST_ID` bigint(20) NOT NULL,
  `USER_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_POST_M_PID`(`POST_ID`) USING BTREE,
  INDEX `IDX_POST_M_UID`(`USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '岗位成员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_privilege
-- ----------------------------
DROP TABLE IF EXISTS `t_privilege`;
CREATE TABLE `t_privilege`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PRIVILEGE_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
  `DESCRIBE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `GROUP_ID` bigint(20) NOT NULL COMMENT '组ID',
  `CREATE_TIME` datetime(6) NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `PLUS_FLAG` tinyint(4) NULL DEFAULT 0 COMMENT '高级版标识',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PRIVILEGE_UK_CODE`(`code`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_privilege_1
-- ----------------------------
DROP TABLE IF EXISTS `t_privilege_1`;
CREATE TABLE `t_privilege_1`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PRIVILEGE_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
  `DESCRIBE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `GROUP_ID` bigint(20) NOT NULL COMMENT '组ID',
  `CREATE_TIME` datetime(6) NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `PLUS_FLAG` tinyint(4) NULL DEFAULT 0 COMMENT '高级版标识',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PRIVILEGE_UK_CODE`(`code`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_privilege_group
-- ----------------------------
DROP TABLE IF EXISTS `t_privilege_group`;
CREATE TABLE `t_privilege_group`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `GROUP_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NOT NULL,
  `LAST_UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `SEQUENCE` int(11) NULL DEFAULT NULL,
  `INSERT INTO merchant.t_privilege_group (GROUP_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PRIVILEGE_GROUP_UK_BUSINESS_CODE`(`BUSINESS_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_privilege_group_1
-- ----------------------------
DROP TABLE IF EXISTS `t_privilege_group_1`;
CREATE TABLE `t_privilege_group_1`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `GROUP_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARENT_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime(6) NOT NULL,
  `LAST_UPDATE_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `SEQUENCE` int(11) NULL DEFAULT NULL,
  `INSERT INTO merchant.t_privilege_group (GROUP_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PRIVILEGE_GROUP_UK_BUSINESS_CODE`(`BUSINESS_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_privilege_group_application
-- ----------------------------
DROP TABLE IF EXISTS `t_privilege_group_application`;
CREATE TABLE `t_privilege_group_application`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PRIVILEGE_GROUP_BUSINESS_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `APPLICATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `GROUP_APP_UK_PRIVILEGE_GROUP_BUSINESS_CODE_APPLICATION_CODE`(`PRIVILEGE_GROUP_BUSINESS_CODE`, `APPLICATION_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限组应用' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_product_line
-- ----------------------------
DROP TABLE IF EXISTS `t_product_line`;
CREATE TABLE `t_product_line`  (
  `ID` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品线编号',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `BUSINESS_CODES` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品线包含的业务编码',
  `DEFAULT_NAV_MENU_TEMPLATEID` bigint(20) NULL DEFAULT NULL COMMENT '默认导航菜单模板ID',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `STAGE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品线系统场景',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品线' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_product_purchase
-- ----------------------------
DROP TABLE IF EXISTS `t_product_purchase`;
CREATE TABLE `t_product_purchase`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CAPACITY` bigint(20) NULL DEFAULT NULL COMMENT '席位上限',
  `IS_EVALUATION` bit(1) NULL DEFAULT NULL COMMENT '是否试用',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业',
  `ORDER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '订单号',
  `PRODUCT_LINE_ID` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品线编码',
  `STATUS` int(11) NULL DEFAULT NULL COMMENT '订单状态',
  `VALIDITY_BEGIN` datetime NULL DEFAULT NULL COMMENT '有效期开始时间',
  `VALIDITY_DURATION` bigint(20) NULL DEFAULT NULL COMMENT '生效天数',
  `VALIDITY_END` datetime NULL DEFAULT NULL COMMENT '有效期结束时间',
  `EXTERNAL_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部系统ID',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_PRODUCT_PURCHASE_ORDER_NO`(`ORDER_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品线订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_promotion_potential_customer
-- ----------------------------
DROP TABLE IF EXISTS `t_promotion_potential_customer`;
CREATE TABLE `t_promotion_potential_customer`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COMPANY_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_MOBILE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `POTENTIAL_PRODUCT` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHANNEL_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_LINKED_UP` bit(1) NOT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_promotion_product
-- ----------------------------
DROP TABLE IF EXISTS `t_promotion_product`;
CREATE TABLE `t_promotion_product`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CHANNEL_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRODUCTS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NOTIFY_MOBILE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol`;
CREATE TABLE `t_protocol`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PROTOCOL_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `SIGN_WAY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CONTRACT_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGN_COUNT` int(11) NULL DEFAULT NULL,
  `PROTOCOL_CREATE_TIME` datetime NULL DEFAULT NULL,
  `VALIDITY_START` datetime NULL DEFAULT NULL,
  `VALIDITY_END` datetime NULL DEFAULT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PROTOCOL_UK_MERCHANT_TENANT_ID`(`MERCHANT_ID`, `TENANT_ID`) USING BTREE,
  INDEX `PROTOCOL_IDX_PROTOCOL_NAME`(`PROTOCOL_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol_attachment
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol_attachment`;
CREATE TABLE `t_protocol_attachment`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PROTOCOL_ID` bigint(20) NOT NULL,
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `PROTOCOL_ATTACHMENT_IDX_PROTOCOL_ID`(`PROTOCOL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol_signer
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol_signer`;
CREATE TABLE `t_protocol_signer`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PROTOCOL_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `IDENTIFY_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `IDENTIFY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SEQ_NUM` int(11) NULL DEFAULT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `IS_DELETE` bit(1) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `PROTOCOL_SIGNER_UK_IDENTIFY`(`NAME`, `IDENTIFY`) USING BTREE,
  INDEX `PROTOCOL_SIGNER_IDX_PROTOCOL_ID`(`PROTOCOL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_protocol_template
-- ----------------------------
DROP TABLE IF EXISTS `t_protocol_template`;
CREATE TABLE `t_protocol_template`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `FILL_FIELD` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RELATION_FIELD` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TEMPLATE_ID` bigint(20) NULL DEFAULT NULL,
  `TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `BUYER_SEAL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SELLER_SEAL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_role
-- ----------------------------
DROP TABLE IF EXISTS `t_role`;
CREATE TABLE `t_role`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ROLE_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `ROLE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
  `MERCHANT_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '企业ID',
  `ROLE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型',
  `IS_BELONG_ADMIN` bit(1) NOT NULL COMMENT '是否管理',
  `DESCRIBE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `IS_DELETE` bit(1) NOT NULL COMMENT '是否删除',
  `SEQ` int(11) NULL DEFAULT NULL COMMENT '顺序',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `IS_ENABLED` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否启用',
  `EXTERNAL_ID` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部系统ID',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ROLE_UK_MERCHANT_ID_ROLE_CODE`(`MERCHANT_ID`, `ROLE_CODE`) USING BTREE,
  INDEX `ROLE_IDX_ROLE_CODE`(`ROLE_CODE`) USING BTREE,
  INDEX `ROLE_IDX_ROLE_NAME`(`ROLE_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_role_application
-- ----------------------------
DROP TABLE IF EXISTS `t_role_application`;
CREATE TABLE `t_role_application`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ROLE_ID` bigint(20) NOT NULL,
  `APPLICATION_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ROLE_APPLICATION_UK_ROLE_ID_APPLICATION_CODE`(`ROLE_ID`, `APPLICATION_CODE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_role_member
-- ----------------------------
DROP TABLE IF EXISTS `t_role_member`;
CREATE TABLE `t_role_member`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `ROLE_ID` bigint(20) NOT NULL COMMENT '角色ID',
  `USER_ID` bigint(20) NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ROLE_MEMBER_ROLE`(`ROLE_ID`) USING BTREE,
  INDEX `IDX_ROLE_MEMBER_USER`(`USER_ID`) USING BTREE,
  INDEX `t_role_member_merchant_id_idx`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_role_privilege
-- ----------------------------
DROP TABLE IF EXISTS `t_role_privilege`;
CREATE TABLE `t_role_privilege`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ROLE_ID` bigint(20) NOT NULL,
  `PRIVILEGE_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ROLE_PRIVILEGE_UK_ROLE_ID_PRIVILEGE_ID`(`ROLE_ID`, `PRIVILEGE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_seal
-- ----------------------------
DROP TABLE IF EXISTS `t_seal`;
CREATE TABLE `t_seal`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL COMMENT '是否删除',
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  `DISABLED` bit(1) NULL DEFAULT NULL COMMENT '禁用',
  `TENANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '租户',
  `ARCHIVE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `FILE_ID` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文档id',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `SOURCE` int(11) NULL DEFAULT NULL COMMENT '来源',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '签章' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sign_order
-- ----------------------------
DROP TABLE IF EXISTS `t_sign_order`;
CREATE TABLE `t_sign_order`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CONTRACT_ID` bigint(20) NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `EXPIRE_TIME` datetime NULL DEFAULT NULL,
  `ORG_LEGAL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_LEGAL_ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PARAM` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_LEGAL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_LEGAL_ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SIGNER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TEMPLATE_ID` bigint(20) NULL DEFAULT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `REQUEST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `REQUEST_NO`(`REQUEST_NO`) USING BTREE,
  INDEX `ORG_NO`(`ORG_NO`) USING BTREE,
  INDEX `SIGNER_NO`(`SIGNER_NO`) USING BTREE,
  INDEX `SIGN_NO`(`SIGNER_NO`, `ORG_NO`, `TEMPLATE_ID`) USING BTREE,
  INDEX `TEMPLATE_ID`(`TEMPLATE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sub_merchant
-- ----------------------------
DROP TABLE IF EXISTS `t_sub_merchant`;
CREATE TABLE `t_sub_merchant`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AUDIT_DESC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUDIT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AUDIT_TIME` datetime NULL DEFAULT NULL,
  `BANK_CITY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_LICENSE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_NO` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_PROVINCE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_DESC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BHA_AUDIT_TIME` datetime NULL DEFAULT NULL,
  `BHA_OPEN_STATUS` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_LICENSE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_LICENSE_IMG_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACT_ID_CARD_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACTS` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACTS_EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CONTACTS_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENTRUSTED_BRANCH_BANK_INFO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ENTRUSTED_BRANCH_BANK_INFO_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EXPRESS_ADDRESS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FIRST_CATEGORY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FUND_ACCOUNT` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FUND_ACCOUNT_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_CARD_TYPE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INDUSTRY` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INTRODUCTION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_BANK_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_BANK_NO` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `INVOICE_MOBILE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `IS_FORBIDDEN` bit(1) NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `LEGAL` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_ID_CARD_FRONT_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_ID_CARD_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LEGAL_ID_CARD_OPPOSITE_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOGO_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ORG_NO_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `POSTCODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PROXY_LETTER_IMG_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_ADDRESS` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_CONTACTS_EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_CONTACTS_MOBILE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_ID_CARD_FRONT_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_ID_CARD_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REGISTER_ID_CARD_OPPOSITE_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SECOND_CATEGORY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAX_NO_IMG_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAXPAYER_IDENTIFICATION_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `UNIFIED_CODE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `BANK_LICENSE_IMG_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `NAME`(`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sync_compensation_message
-- ----------------------------
DROP TABLE IF EXISTS `t_sync_compensation_message`;
CREATE TABLE `t_sync_compensation_message`  (
  `serial_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列号',
  `message_key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '消息key，目前有org,legal,company',
  `company_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业ID',
  `message_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '如果为部门或法人，为对应的变更数据ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '变更消息类型：modify/add/delete',
  `time_stamp` bigint(20) NOT NULL COMMENT '平台消息创建时间戳',
  `first_consume_time` bigint(20) NOT NULL COMMENT '消息首次消费时间戳',
  `next_consume_time` bigint(20) NULL DEFAULT NULL COMMENT '下次消息消费时间戳',
  `status` bit(1) NOT NULL COMMENT '消息状态，1：已完成补偿，0：未完成补偿',
  PRIMARY KEY (`serial_num`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '同步补偿消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sync_compensation_message_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sync_compensation_message_record`;
CREATE TABLE `t_sync_compensation_message_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `serial_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列号',
  `exec_time` datetime NOT NULL COMMENT '执行时间',
  `exec_result` bit(1) NOT NULL COMMENT '执行结果，1：成功，0：失败',
  `err_msg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '执行失败时的错误消息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '同步补偿消息记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_synchronization_details
-- ----------------------------
DROP TABLE IF EXISTS `t_synchronization_details`;
CREATE TABLE `t_synchronization_details`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SYNCHRONIZATION_RECORD_ID` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NOT NULL,
  `MESSAGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_IZATION_RECORD_ID`(`SYNCHRONIZATION_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_synchronous_record
-- ----------------------------
DROP TABLE IF EXISTS `t_synchronous_record`;
CREATE TABLE `t_synchronous_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NOT NULL,
  `ACCESS_TOKEN_ID` bigint(20) NULL DEFAULT NULL,
  `APP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EVENT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `EVENT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `APPLICATION_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SYNCHRONOUS_STATE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `LOCKED_TIME` datetime NULL DEFAULT NULL,
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE,
  INDEX `I_SYS_MQ_LOG_TIME`(`CREATE_TIME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tag
-- ----------------------------
DROP TABLE IF EXISTS `t_tag`;
CREATE TABLE `t_tag`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标签名称',
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `TARGET_TYPE` int(11) NULL DEFAULT NULL COMMENT '目标实体类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tag_option
-- ----------------------------
DROP TABLE IF EXISTS `t_tag_option`;
CREATE TABLE `t_tag_option`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标签项名称',
  `TAG_ID` bigint(20) NULL DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '标签选项' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tag_ref
-- ----------------------------
DROP TABLE IF EXISTS `t_tag_ref`;
CREATE TABLE `t_tag_ref`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `OWNER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_ID_LONG` bigint(20) NULL DEFAULT NULL COMMENT '实体ID',
  `OWNER_TYPE` int(11) NULL DEFAULT NULL COMMENT '实体类型',
  `TAG_ID` bigint(20) NULL DEFAULT NULL,
  `TAG_OPTION_ID` bigint(20) NULL DEFAULT NULL,
  `TARGET_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TARGET_ID_LONG` bigint(20) NULL DEFAULT NULL,
  `TARGET_TYPE` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_TAG_REF_UNIQUE`(`TAG_OPTION_ID`, `TARGET_ID`) USING BTREE,
  INDEX `IDX_TAG_REF_ENTITY`(`TARGET_ID`) USING BTREE,
  INDEX `IDX_TAG_REF_ENTITY_LONG`(`TARGET_ID_LONG`) USING BTREE,
  INDEX `IDX_TAG_REF_OPTION`(`TAG_OPTION_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '标签引用' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_declare_info
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_declare_info`;
CREATE TABLE `t_tax_declare_info`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纳税申报ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL,
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `acceptId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '受理人ID',
  `authStatus` int(11) NULL DEFAULT NULL COMMENT '认证状态',
  `DECLARE_PASSWORD` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报密码',
  `LEGAL_ID` bigint(20) NULL DEFAULT NULL COMMENT '法人实体ID',
  `SUBSECTOR_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分部门名称',
  `SUBSECTOR_NO` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分部门编号',
  `ERROR_INFO` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `TAX_AUDIT_STATUS` int(11) NULL DEFAULT NULL COMMENT '纳税认证状态',
  `TAX_AUDIT_TIME` datetime(6) NULL DEFAULT NULL COMMENT '纳税主体审核时间',
  `TAX_AUTH_WAY` int(11) NULL DEFAULT NULL COMMENT '纳税认证方式',
  `TAX_ORGAN_NUMBER` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税登记序号',
  `SERIAL_NUMBER` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `TAX_ORGAN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `TAX_NUMBER` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tenant
-- ----------------------------
DROP TABLE IF EXISTS `t_tenant`;
CREATE TABLE `t_tenant`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '租户ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `DOMAINS` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户域名',
  `NAME` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_todo
-- ----------------------------
DROP TABLE IF EXISTS `t_todo`;
CREATE TABLE `t_todo`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `APP_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用源',
  `APP_TASK_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务任务code',
  `CONTENT` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '内容',
  `END_TIME` datetime(6) NULL DEFAULT NULL COMMENT '待办截止时间',
  `OPERATE` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '选项列表',
  `SPONSOR_USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '发起者用户ID',
  `STATUS` int(11) NULL DEFAULT NULL COMMENT '状态',
  `TASK_STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任务状态',
  `TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '待办用户ID',
  `EXTEND_INFO` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扩展信息',
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `BUSINESS_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务数据唯一key',
  `URL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '跳转地址',
  `TERMINAL_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '终端类型',
  `TODO_TEXT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '待办' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_todo_something
-- ----------------------------
DROP TABLE IF EXISTS `t_todo_something`;
CREATE TABLE `t_todo_something`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `RECORD_STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `UPDATE_TIME` datetime NULL DEFAULT NULL,
  `VERSION` int(11) NOT NULL,
  `ADDITIONAL_DATA` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `BUSSESS_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSSESS_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CLOSE_TIME` datetime NULL DEFAULT NULL,
  `CONTENT` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `DEAL_USER_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `EFFECT_TIME` datetime NULL DEFAULT NULL,
  `MERCHANT_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PRIORITY` int(11) NULL DEFAULT NULL,
  `REMARK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TITLE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TODO_USER_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TODO_USER_ROLE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_ORIGIN_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_ORIGIN_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `URL` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TODO_CREATE_STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TODO_SOMETHING_USER_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `KEY_BUSINESS_ORIGIN_ID`(`BUSINESS_ORIGIN_ID`) USING BTREE,
  INDEX `KEY_BUSSESS_ID`(`BUSSESS_ID`) USING BTREE,
  INDEX `KEY_TODO_USER_ID`(`TODO_USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user`  (
  `ID` bigint(20) NOT NULL COMMENT '用户ID',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `DELETED` bit(1) NULL DEFAULT NULL,
  `DELETED_TIME` datetime(6) NULL DEFAULT NULL,
  `DISABLED` bit(1) NULL DEFAULT NULL,
  `TENANT_ID` bigint(20) NULL DEFAULT NULL,
  `CELLPHONE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录手机号',
  `DEFAULT_BANK_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '默认使用的银行卡',
  `DEFAULT_MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '默认进入的企业',
  `EMAIL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录电子邮箱',
  `IS_AUTH` bit(1) NULL DEFAULT NULL COMMENT '是否完成实名认证',
  `LAST_LOGIN_IP` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '上一次登录的IP',
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户名',
  `PERSONAL_ID` bigint(20) NULL DEFAULT NULL COMMENT '个人信息ID',
  `CELLPHONE_END4` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号后四位',
  `EXTERNAL_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部系统ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_USER_CELLPHONE`(`CELLPHONE`) USING BTREE,
  INDEX `IDX_USER_NAME`(`NAME`) USING BTREE,
  INDEX `t_user_external_id_idx`(`EXTERNAL_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_user_inlet
-- ----------------------------
DROP TABLE IF EXISTS `t_user_inlet`;
CREATE TABLE `t_user_inlet`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL,
  `INLET_ID` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_whtax
-- ----------------------------
DROP TABLE IF EXISTS `t_whtax`;
CREATE TABLE `t_whtax`  (
  `ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `BUSINESS_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `FEE_RATE` double NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_workbench_calendar
-- ----------------------------
DROP TABLE IF EXISTS `t_workbench_calendar`;
CREATE TABLE `t_workbench_calendar`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CALENDAR_USER_ID` bigint(20) NOT NULL COMMENT '提醒用户编号',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '最后修改时间',
  `REMIND_CONTENT` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提醒内容',
  `REMIND_DATE` date NOT NULL COMMENT '提醒日期',
  `REMIND_STATUS` int(11) NOT NULL COMMENT '提醒状态',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `CALENDAR_USER_ID_IDX`(`CALENDAR_USER_ID`) USING BTREE,
  INDEX `REMIND_DATE_IDX`(`REMIND_DATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
