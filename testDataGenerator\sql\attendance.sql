/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : attendance

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:23:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `BLOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `SCHED_NAME`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CALENDAR_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CALENDAR` blob NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CRON_EXPRESSION` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TIME_ZONE_ID` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ENTRY_ID` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `FIRED_TIME` bigint(13) NOT NULL,
  `SCHED_TIME` bigint(13) NOT NULL,
  `PRIORITY` int(11) NOT NULL,
  `STATE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `ENTRY_ID`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TRIG_INST_NAME`(`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY`(`SCHED_NAME`, `INSTANCE_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_FT_J_G`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_T_G`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TG`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `JOB_CLASS_NAME` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_DURABLE` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IS_UPDATE_DATA` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_J_REQ_RECOVERY`(`SCHED_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_J_GRP`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `LOCK_NAME` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `LOCK_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `LAST_CHECKIN_TIME` bigint(13) NOT NULL,
  `CHECKIN_INTERVAL` bigint(13) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `REPEAT_COUNT` bigint(7) NOT NULL,
  `REPEAT_INTERVAL` bigint(12) NOT NULL,
  `TIMES_TRIGGERED` bigint(10) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `STR_PROP_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `STR_PROP_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `STR_PROP_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `INT_PROP_1` int(11) NULL DEFAULT NULL,
  `INT_PROP_2` int(11) NULL DEFAULT NULL,
  `LONG_PROP_1` bigint(20) NULL DEFAULT NULL,
  `LONG_PROP_2` bigint(20) NULL DEFAULT NULL,
  `DEC_PROP_1` decimal(13, 4) NULL DEFAULT NULL,
  `DEC_PROP_2` decimal(13, 4) NULL DEFAULT NULL,
  `BOOL_PROP_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `BOOL_PROP_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `JOB_GROUP` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `NEXT_FIRE_TIME` bigint(13) NULL DEFAULT NULL,
  `PREV_FIRE_TIME` bigint(13) NULL DEFAULT NULL,
  `PRIORITY` int(11) NULL DEFAULT NULL,
  `TRIGGER_STATE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TRIGGER_TYPE` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `START_TIME` bigint(13) NOT NULL,
  `END_TIME` bigint(13) NULL DEFAULT NULL,
  `CALENDAR_NAME` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `MISFIRE_INSTR` smallint(2) NULL DEFAULT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_J`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_C`(`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_T_G`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_STATE`(`SCHED_NAME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_STATE`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_G_STATE`(`SCHED_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NEXT_FIRE_TIME`(`SCHED_NAME`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST`(`SCHED_NAME`, `TRIGGER_STATE`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_approval
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_approval`;
CREATE TABLE `t_attend_approval`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `BIZ_SUIT_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批套件类型',
  `PROCESS_ID` bigint(20) NOT NULL COMMENT '流程id',
  `IS_ENABLE` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `DEFAULT_PROCESS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤组-审批流程表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_device_sign_record
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_device_sign_record`;
CREATE TABLE `t_attend_device_sign_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `co_id` bigint(20) NOT NULL COMMENT '企业ID',
  `attend_id` bigint(20) NOT NULL COMMENT '所属考勤组ID',
  `record_source` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '记录来源，ZK-中控，DL-得力',
  `dev_sn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设备序列号',
  `zk_id` bigint(20) NULL DEFAULT NULL COMMENT '中控-考勤数据id',
  `dl_next_id` bigint(20) NULL DEFAULT 0 COMMENT '得力-考勤数据id',
  `person_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员姓名',
  `person_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员编码，对应t_person_send表的card_number',
  `dept_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门编码',
  `check_time` datetime NULL DEFAULT NULL COMMENT '打卡时间',
  `check_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡方式，得力不同打卡方式，有不同的json数据',
  `face_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '人脸信息',
  `ext_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '其他可能用到的补充信息，一般为json数据',
  `handle_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'NOT_DEAL' COMMENT '处理状态，NOT_DEAL-未处理，IGNORE_DEAL-忽略处理，DEALING-处理中，DEAL_SUCCESS-处理成功，DEAL_FAIL-处理失败',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '失败原因',
  `last_handle_time` datetime NULL DEFAULT NULL COMMENT '最近一次的处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤设备打卡记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_group
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_group`;
CREATE TABLE `t_attend_group`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业id',
  `AG_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '考勤组名称',
  `TIME_ZONE` int(11) NULL DEFAULT NULL COMMENT '时区',
  `ATTEND_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '考勤组类型(FIX:固定班,SCHEDULING=排班,FREE=自由班制)',
  `ALLOWED_MISSING_ATTEND` tinyint(4) NULL DEFAULT NULL COMMENT '工作日不打卡缺卡',
  `ALLOWED_HOLIDAY_CALENDER` tinyint(4) NULL DEFAULT NULL COMMENT '是否启用排休日历',
  `HOLIDAY_CALENDER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自动排休日历',
  `HOLIDAY_SIGN_APPROVAL` tinyint(4) NULL DEFAULT NULL COMMENT '休息日打卡审批',
  `ALLOWED_CHOOSE_SHIFT` tinyint(4) NULL DEFAULT NULL COMMENT '未排班可选择班次',
  `ALLOWED_SIGN` tinyint(4) NULL DEFAULT NULL COMMENT '未排班可打卡',
  `ALLOWED_QUICKLY` tinyint(4) NULL DEFAULT NULL COMMENT '急速打卡开关',
  `QUICKLY_MINUTES` int(11) NULL DEFAULT NULL COMMENT '急速打卡(提前分钟数)',
  `ALLOWED_OUTSIDE` tinyint(4) NULL DEFAULT NULL COMMENT '外勤打卡开关',
  `OUTSIDE_APPROVAL` tinyint(4) NULL DEFAULT NULL COMMENT '外勤审批',
  `OUTSIDE_REMARK` tinyint(4) NULL DEFAULT NULL COMMENT '外勤备注',
  `OUTSIDE_IMAGES` tinyint(4) NULL DEFAULT NULL COMMENT '外勤拍照',
  `OVERTIME_RULE_ID` bigint(20) NULL DEFAULT NULL COMMENT '加班规则id',
  `SUPPLEMENT_RULE_ID` bigint(20) NULL DEFAULT NULL COMMENT '补卡规则id',
  `EFFECT_NOW` tinyint(4) NULL DEFAULT 1 COMMENT '是否立即生效',
  `APPROVAL_SWITCH` tinyint(4) NOT NULL DEFAULT 0 COMMENT '审批开关',
  `IS_DELETED` tinyint(4) NULL DEFAULT 0 COMMENT '逻辑删除',
  `ALLOW_DEVICE_SYNC_RECORD` tinyint(4) NOT NULL DEFAULT 0 COMMENT '允许从考勤机设备同步打卡记录 0-不允许 1-允许',
  `CREATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `ENABLE_FACE_OCR` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否启动人脸识别',
  `VERIRY_FACE_OCR` tinyint(4) NOT NULL DEFAULT 0 COMMENT '员工首次上传考勤机人脸照片，是否开启真人验证。',
  `NOTIFY_HIGH_TEMPERATURE` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否开启异常体温提醒',
  `MIN_TEMPERATURE` decimal(4, 1) NOT NULL DEFAULT 0.0 COMMENT '启异常体温提醒的最低体温',
  `ENABLE_WIFI` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启动WIFI打卡',
  `WIFI_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'WIFI名称',
  `WIFI_MAC_ADDRESS` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'WIFI地址',
  `UNCHECKED_GROUP` tinyint(4) NULL DEFAULT NULL COMMENT '是否为不校验打卡考勤组',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_place
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_place`;
CREATE TABLE `t_attend_place`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `PLACE_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打卡地点名称',
  `PLACE_ALIAS` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡地点别名',
  `LONGITUDE` decimal(32, 10) NOT NULL COMMENT '经度',
  `LATITUDE` decimal(32, 10) NOT NULL COMMENT '纬度',
  `ERROR_RANGE` int(11) NOT NULL COMMENT '误差范围',
  `DESCRIPTION` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '说明备注',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ATTEND_PLACE_CO_ATTEND`(`CO_ID`, `ATTEND_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '位置打卡' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_rel
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_rel`;
CREATE TABLE `t_attend_rel`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `TAXSUB_ID` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `ATTEND_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '考勤组id',
  `REL_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '业务类型',
  `TARGET_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '关联目标id',
  `TARGET_USER_ID` bigint(20) NULL DEFAULT NULL,
  `TARGET_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联目标名称',
  `TARGET_USER_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TARGET_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '关联目标类型(EMPLOYEE:员工,DEPARTMENT:部门)',
  `IS_ENABLE` tinyint(1) NULL DEFAULT 1,
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ATTEND_CO_TARGET_ID`(`CO_ID`, `TARGET_USER_ID`, `ATTEND_ID`) USING BTREE,
  INDEX `IDX_ATTEND_REL_CO_TAXSUB_TARGET`(`CO_ID`, `TAXSUB_ID`, `TARGET_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_rel_tomorrow
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_rel_tomorrow`;
CREATE TABLE `t_attend_rel_tomorrow`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `TAXSUB_ID` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `ATTEND_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '考勤组id',
  `REL_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '业务类型',
  `TARGET_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '关联目标id',
  `TARGET_USER_ID` bigint(20) NULL DEFAULT NULL,
  `TARGET_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联目标名称',
  `TARGET_USER_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TARGET_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '关联目标类型(EMPLOYEE:员工,DEPARTMENT:部门)',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤关系次日生效表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_wifi
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_wifi`;
CREATE TABLE `t_attend_wifi`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `WIFI_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'WIFI名称',
  `WIFI_ALIAS` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'WIFI别名',
  `MAC` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Mac',
  `DESCRIPTION` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '说明备注',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'WIFI打卡' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_working_shift
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_working_shift`;
CREATE TABLE `t_attend_working_shift`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `SHIFT_ORDER` int(11) NULL DEFAULT NULL COMMENT '序号(0为默认1-7为一周排序)',
  `IS_REST_DAY` tinyint(4) NULL DEFAULT NULL COMMENT '是否为休息日',
  `WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次ID',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ATTEND_WORKING_SHIFT_CO_ATTEND`(`CO_ID`, `ATTEND_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤组-班次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_working_shift_tomorrow
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_working_shift_tomorrow`;
CREATE TABLE `t_attend_working_shift_tomorrow`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `REAL_ATTEND_WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '原表ID',
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `SHIFT_ORDER` int(11) NULL DEFAULT NULL COMMENT '序号(0为默认1-7为一周排序)',
  `IS_REST_DAY` tinyint(4) NULL DEFAULT NULL COMMENT '是否为休息日',
  `WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次ID',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤组-班次次日生效表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_zone
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_zone`;
CREATE TABLE `t_attend_zone`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `co_id` bigint(20) NOT NULL COMMENT '部门ID',
  `zone_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '设备平台区域名称',
  `belong_platform` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '所属平台ZK-中控，DL-得力',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '最后一次同步时间',
  `oper_user_id` bigint(20) NULL DEFAULT NULL COMMENT '操作人id',
  `oper_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '设备平台区域信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_attend_zone_person
-- ----------------------------
DROP TABLE IF EXISTS `t_attend_zone_person`;
CREATE TABLE `t_attend_zone_person`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `merchant_id` bigint(20) NOT NULL COMMENT '阿拉钉平台人员所属部门',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `zone_id` bigint(20) NOT NULL COMMENT '人员所属区域ID',
  `belong_platform` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '所属平台ZK-中控，DL-得力',
  `card_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '考勤卡号，即下发到设备上的人员编码',
  `face_status` int(11) NOT NULL DEFAULT 0 COMMENT '人脸信息 0-不存在，1-已存在',
  `send_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'NOT_SEND' COMMENT '下发状态 NOT_SEND-未下发，SENDING-下发中，HAS_SEND-已下发',
  `send_result` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下发结果 SUCCESS-成功，FAIL-失败',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '失败原因, sendResult为FAIL时放置',
  `last_send_time` datetime NULL DEFAULT NULL COMMENT '最后一次下发时间',
  `last_notice_time` datetime NULL DEFAULT NULL COMMENT '最后一次通知时间',
  `person_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'ENABLE' COMMENT '人员状态 ENABLE-有效，DISABLE-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '区域人员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_balance_minus_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_balance_minus_detail`;
CREATE TABLE `t_balance_minus_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `COM_ID` bigint(20) NOT NULL COMMENT '企业id',
  `EMP_ID` bigint(20) NOT NULL,
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `LEAVE_ID` bigint(20) NOT NULL,
  `BALANCE_RECO_ID` bigint(20) NOT NULL COMMENT '余额记录id',
  `OP_VALUE` double(10, 2) NOT NULL COMMENT '详情值',
  `LEAVE_DATE` timestamp NULL DEFAULT NULL COMMENT '请假时间',
  `OP_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作类型',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_LEAVE_USER_ID`(`COM_ID`, `USER_ID`, `LEAVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人假期余额扣减详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_column
-- ----------------------------
DROP TABLE IF EXISTS `t_column`;
CREATE TABLE `t_column`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `COLUMN_ID` bigint(20) NULL DEFAULT NULL COMMENT '报表列ID',
  `TYPE` bigint(20) NULL DEFAULT NULL COMMENT '报表列类型',
  `NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表列名',
  `ALIAS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '列报表',
  `STATUS` bigint(20) NULL DEFAULT NULL COMMENT '报表列的状态',
  `SUB_TYPE` bigint(20) NULL DEFAULT NULL COMMENT '子类型',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤统计钉钉列定义表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_column_value
-- ----------------------------
DROP TABLE IF EXISTS `t_column_value`;
CREATE TABLE `t_column_value`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `USER_ID` bigint(20) NOT NULL COMMENT '员工管理user_id',
  `DING_USER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '钉钉的user_id',
  `DATE` datetime NULL DEFAULT NULL COMMENT '日期',
  `VALUE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '列值',
  `COLUMN_ID` bigint(20) NULL DEFAULT NULL COMMENT '报表列ID',
  `COLUMN_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '假期名称',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '考勤统计钉钉列值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_device_platform_config
-- ----------------------------
DROP TABLE IF EXISTS `t_device_platform_config`;
CREATE TABLE `t_device_platform_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `co_id` bigint(20) NULL DEFAULT NULL COMMENT '阿拉钉平台的企业ID',
  `url_prefix` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '平台调用地址前缀',
  `static_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '中控平台-静态密钥',
  `platform_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '平台类型 ZK-中控 DL-得力',
  `dl_dept_init_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'NOT_INIT' COMMENT '得力平台-部门初始化状态 NOT_INIT-未初始化，HAS_INIT-已初始化',
  `dl_record_init_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'NOT_INIT' COMMENT '得力平台-考勤初始化状态NOT_INIT-未初始化，HAS_INIT-已初始化',
  `platform_app_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '得力平台-appKey',
  `platform_app_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '得力平台-appSecret',
  `agreed` int(11) NOT NULL COMMENT '是否同意协议 0-不同意，1-已同意',
  `ext_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '其他扩展数据',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '设备平台配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_ding_approval_result
-- ----------------------------
DROP TABLE IF EXISTS `t_ding_approval_result`;
CREATE TABLE `t_ding_approval_result`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DURATION_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单单位',
  `SUB_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子类型名称',
  `PROCINST_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批单id',
  `WORK_DATE` date NULL DEFAULT NULL COMMENT '查询日期',
  `EMP_ID` bigint(20) NOT NULL COMMENT '员工id',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `DURATION` double(6, 2) NULL DEFAULT 0.00 COMMENT '时长',
  `CURRENT_DURATION` double(6, 2) NULL DEFAULT 0.00 COMMENT '当日时长',
  `DING_USER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '钉钉员工id',
  `APPROVAL_TYPE` tinyint(4) NOT NULL COMMENT '审批类型   1加班  2出差   3请假',
  `BEGIN_TIME` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `END_TIME` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_DING_APPROVAL`(`CO_ID`, `EMP_ID`, `WORK_DATE`, `APPROVAL_TYPE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '钉钉审批结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_ding_stats_result
-- ----------------------------
DROP TABLE IF EXISTS `t_ding_stats_result`;
CREATE TABLE `t_ding_stats_result`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `DING_USER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '钉钉员工id',
  `EMP_ID` bigint(20) NOT NULL COMMENT '阿拉钉员工id',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `WORK_DATE` date NOT NULL COMMENT '查询日期',
  `LATE_LENGTH` int(11) NOT NULL DEFAULT 0 COMMENT '日迟到总时长',
  `LEAVE_EARLY_LENGTH` int(11) NOT NULL DEFAULT 0 COMMENT '缺勤总时长',
  `IS_ABSENT` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否缺勤',
  `LATE_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT '迟到次数',
  `MUST_WORK_LENGTH` int(11) NOT NULL DEFAULT 0 COMMENT '应出勤时长',
  `LEAVE_EARLY_COUNT` int(11) NULL DEFAULT 0 COMMENT '早退次数',
  `OUTSIDE_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT '外勤次数',
  `ABSENT_BEGIN_WORK_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT '上班缺卡次数',
  `ABSENT_END_WORK_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT '下班缺卡次数',
  `IS_WORK_DAY` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否为工作日',
  `GO_OUT_LENGTH` int(11) NOT NULL DEFAULT 0 COMMENT '外出时长',
  `APPROVE_PASS_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT '补卡次数',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_DING_STATS_RESULT`(`CO_ID`, `USER_ID`, `EMP_ID`, `WORK_DATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '钉钉员工数据统计同步表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_care_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_care_rule`;
CREATE TABLE `t_emp_care_rule`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `OR_ID` bigint(20) NULL DEFAULT NULL COMMENT '加班规则ID',
  `OVERTIME_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '加班计算单位',
  `OVERTIME_TIME` int(11) NULL DEFAULT NULL COMMENT '加班时长',
  `OVERTIME_ALERT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提醒人(json数组)',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工关爱触发规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_face
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_face`;
CREATE TABLE `t_emp_face`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '考勤组id',
  `USER_ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '平台员工id',
  `FACE_URL` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人脸图片地址（这里不存ID，直接存url，列表页需要，避免频繁调用文档服务接口获取url）',
  `FACE_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人脸图片id',
  `UPLOAD_TIME` datetime NULL DEFAULT NULL COMMENT '首次上传图片的时间',
  `AI_CHECK_COUNT` int(11) NOT NULL DEFAULT 0 COMMENT 'AI验证次数',
  `FACE_STATUS` tinyint(4) NOT NULL DEFAULT 1 COMMENT '人脸状态：0-正常；1-未录入；2-异常(产品排序规则：人脸状态倒序（异常、未录入、正常）',
  `AI_CHECK_STATUS` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'AI验证：0-无；1-已通过；2-未通过（产品排序规则：倒序（未通过、已通过、无）',
  `ENABLE` tinyint(4) NOT NULL DEFAULT 1 COMMENT '员工是否启动：1-启用，0-未启用',
  `CHECK_WAY` tinyint(4) NOT NULL DEFAULT 0 COMMENT '验证方式：0-无；1-管理员修改；2-AI验证',
  `CHECK_TIME` datetime NULL DEFAULT NULL COMMENT '验证时间',
  `NOTIFY_TIME` datetime NULL DEFAULT NULL COMMENT '微信通知的时间',
  `UPDATED_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `LAST_FACE_SIGN_AT` datetime NULL DEFAULT NULL COMMENT '最后一次人脸打卡的时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_UK_CO_USER`(`CO_ID`, `USER_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工人脸管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_overtime_record
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_overtime_record`;
CREATE TABLE `t_emp_overtime_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `TAXSUB_ID` bigint(20) NOT NULL COMMENT '公司id',
  `DEPT_ID` bigint(20) NOT NULL COMMENT '部门id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `EMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '员工ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `OR_ID` bigint(20) NULL DEFAULT NULL COMMENT '加班规则ID',
  `OVERTIME_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '加班类型',
  `STATISTICS_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '加班统计类型',
  `APPLY_START` datetime NULL DEFAULT NULL COMMENT '加班申请开始时间',
  `APPLY_END` datetime NULL DEFAULT NULL COMMENT '加班申请结束时间',
  `APPLY_DURATION` int(11) NULL DEFAULT NULL COMMENT '加班申请时长',
  `APPROVE_START` datetime NULL DEFAULT NULL COMMENT '加班审批开始时间',
  `APPROVE_END` datetime NULL DEFAULT NULL COMMENT '加班审批结束时间',
  `APPROVE_DURATION` int(11) NULL DEFAULT NULL COMMENT '加班审批时长',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工加班明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_execute_task
-- ----------------------------
DROP TABLE IF EXISTS `t_execute_task`;
CREATE TABLE `t_execute_task`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `TASK_TAG` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务标识（需要相同任务时具有唯一性，不要求全局唯一，要求任务类型+任务标识唯一）',
  `EMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '员工标识',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `LEAVE_ID` int(11) NULL DEFAULT NULL COMMENT '假期标识',
  `TASK_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型（假期余额发放、新员工假期初始化、审批假期余额变更）',
  `RELATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联任务标识ID',
  `TASK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务状态（INIT,PENDING、COMPLETE）',
  `EXECUTE_START` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务开始执行时间',
  `EXECUTE_END` timestamp NULL DEFAULT NULL COMMENT '任务结束时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `TASK_PARAM` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '任务参数',
  `REMARK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `UK_TASK_TAG_TYPE`(`TASK_TAG`, `TASK_TYPE`) USING BTREE,
  INDEX `IDX_CO_USER_TASK_ID`(`COMP_ID`, `USER_ID`, `TASK_TAG`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '任务执行表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_leave
-- ----------------------------
DROP TABLE IF EXISTS `t_leave`;
CREATE TABLE `t_leave`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LEAVE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '假期名称',
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `BIZ_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '假期类型',
  `WHEN_CAN_LEAVE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '新员工请假规则',
  `WHEN_CAN_LIMIT` int(11) NULL DEFAULT NULL COMMENT '新员工请假限制时间',
  `LEAVE_UNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '最小请假单位',
  `LEAVE_COUNT_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请假时长核算',
  `HOURS_TO_DAY` int(11) NULL DEFAULT NULL COMMENT '时长折算天',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `DEL_FLAG` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除标记   1已删除  0未删除',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '假期表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_leave_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_leave_rule`;
CREATE TABLE `t_leave_rule`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LEAVE_ID` int(11) NOT NULL COMMENT '假期标识',
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `HOW_TO_RELEASE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '余额发放方式',
  `WHEN_RELEASE_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发放日期类型',
  `WHEN_TO_RELEASE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发放日期',
  `LEAVE_RULE_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '额度配置规则',
  `RELEASE_BALANCE` int(11) NULL DEFAULT NULL COMMENT '发放额度',
  `WORK_BALANCE_RULE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工龄配额规则',
  `ENTRY_BALANCE_RULE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '司龄配额规则',
  `LESS_THAN_ONE_YEAR_ROUND` tinyint(4) NULL DEFAULT NULL COMMENT '是否按实际工作时长发放',
  `EXPIRE_DATE_TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '余额有效期类型',
  `EXPIRE_DATE_VALUE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效期值',
  `POSTPONE_DAY_CHECKED` tinyint(4) NULL DEFAULT NULL COMMENT '是否允许延期',
  `POSTPONE_TIME_UNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '延期单位(年、月、日)',
  `POSTPONE_DAY` int(11) NULL DEFAULT NULL COMMENT '延期数量',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '余额规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_leave_scope
-- ----------------------------
DROP TABLE IF EXISTS `t_leave_scope`;
CREATE TABLE `t_leave_scope`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LEAVE_ID` int(11) NOT NULL COMMENT '假期标识',
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `TYPE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '适用类型（组织、职员）',
  `UNIQUE_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型对应唯一标识',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '假期适用范围表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_notify_setting
-- ----------------------------
DROP TABLE IF EXISTS `t_notify_setting`;
CREATE TABLE `t_notify_setting`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `TO_WORK_NOTIFY` tinyint(1) NOT NULL DEFAULT 0 COMMENT '上班打卡提醒',
  `FROM_WORK_NOTIFY` tinyint(1) NOT NULL DEFAULT 0 COMMENT '下班打卡提醒',
  `FORGET_SIGN_NOTIFY` tinyint(1) NOT NULL DEFAULT 0 COMMENT '忘记打卡提醒',
  `ABSENT_NOTIFY` tinyint(1) NOT NULL DEFAULT 0 COMMENT '补卡卡提醒',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CO_ID`(`CO_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '消息提醒设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_overtime_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_overtime_rule`;
CREATE TABLE `t_overtime_rule`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `RULE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '加班规则名称',
  `EMP_CARE` tinyint(4) NULL DEFAULT NULL COMMENT '员工关爱',
  `IS_DEFAULT` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否默认规则',
  `OVERTIME_STATISTICS_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '加班计时单位',
  `OVERTIME_STATISTICS_CEIL_HOUR` int(11) NOT NULL COMMENT '加班折算取整规则小时',
  `OVERTIME_STATISTICS_CEIL_DAY` int(11) NOT NULL COMMENT '加班折算取整规则天',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '加班规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_overtime_rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_overtime_rule_detail`;
CREATE TABLE `t_overtime_rule_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `OR_ID` bigint(20) NOT NULL COMMENT '加班规则id',
  `RULE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '规则类型(WORKDAY:工作日,RESTDAY休息日,HOLIDAY节假日)',
  `ALLOWED_OVERTIME` tinyint(4) NOT NULL COMMENT '允许加班',
  `OVERTIME_CALC_RULE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '加班时长计算方式',
  `ALLOWED_OVERTIME_SETTING` tinyint(4) NOT NULL COMMENT '加班时间设置开关',
  `ALLOW_IGNORE_OVERTIME` tinyint(4) NULL DEFAULT NULL COMMENT '少于N分钟不计入加班开关',
  `IGNORE_OVERTIME` int(11) NULL DEFAULT NULL COMMENT '少于N分钟不计入加班',
  `ALLOW_MAX_OVERTIME` tinyint(4) NULL DEFAULT NULL COMMENT '当天加班时长最多不能超过N分钟开关',
  `MAX_OVERTIME` int(11) NULL DEFAULT NULL COMMENT '当天加班时长最多不能超过N分钟',
  `ALLOW_START_OVERTIME` tinyint(4) NULL DEFAULT NULL COMMENT '下班N分钟后，开始记入加班开关',
  `START_OVERTIME` int(11) NULL DEFAULT NULL COMMENT '下班N分钟后，开始记入加班',
  `REST_TIME_INTERVAL` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '休息时间段(json数组)',
  `ALLOWED_LEAVE_OR_PAY` tinyint(4) NOT NULL COMMENT '记为加班费或调休开关',
  `LEAVE_OR_PAY_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调休或加班费类型',
  `TO_DAYS_OFF_HOLIDAY_ID` int(11) NULL DEFAULT NULL COMMENT '记为调休时长(对应的调休假的ID)',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '加班规则明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_person_leave_balance
-- ----------------------------
DROP TABLE IF EXISTS `t_person_leave_balance`;
CREATE TABLE `t_person_leave_balance`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `LEAVE_ID` int(11) NOT NULL COMMENT '假期标识',
  `EMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '职员标识',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `BALANCE` double(10, 2) NULL DEFAULT 0.00 COMMENT '假期余额',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `VERSION_FLAG` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `T_PERSON_LEAVE_BALANCE_EMP_ID_LEAVE_ID_uindex`(`EMP_ID`, `LEAVE_ID`) USING BTREE,
  INDEX `IDX_LEAVE_USER_ID`(`COMP_ID`, `USER_ID`, `LEAVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人假期余额表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_person_leave_balance_reco
-- ----------------------------
DROP TABLE IF EXISTS `t_person_leave_balance_reco`;
CREATE TABLE `t_person_leave_balance_reco`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LEAVE_ID` int(11) NOT NULL COMMENT '假期标识',
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `EMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '职员标识',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `OP_USER_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作者',
  `OP_REASON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '理由',
  `OP_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作类型（手动调减、调增、系统发放。。。）',
  `LEAVE_UNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作单位',
  `OP_VALUE` double(10, 2) NOT NULL COMMENT '调整额度',
  `AFERT_BALANCE_CHANGE` double(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更记录后剩余余额',
  `RELATION_ORDER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联单号（例如:审批单号）',
  `REAL_EXPIRE_DATE` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '真实过期时间',
  `EXPIRE_DATE` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `TAKE_EFFECT_DATE` timestamp NULL DEFAULT NULL COMMENT '生效时间',
  `BIZ_TAG` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DEL_STATUS` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标志',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `OP_USER_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作者名称',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_LEAVE_USER_ID`(`COMP_ID`, `USER_ID`, `LEAVE_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人假期余额变更记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_person_workflow_order
-- ----------------------------
DROP TABLE IF EXISTS `t_person_workflow_order`;
CREATE TABLE `t_person_workflow_order`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `LEAVE_ID` int(11) NULL DEFAULT NULL COMMENT '假期标识',
  `COMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司标识',
  `EMP_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '职员标识',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `RELATION_ORDER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批单号',
  `REASON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '原因',
  `VALUE` double(10, 2) NULL DEFAULT NULL COMMENT '时长',
  `LEAVE_UNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请假单位（DAY、HALF_DAY、HOUR）',
  `LEAVE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批类型（请假、出差、外勤、补卡）',
  `LEAVE_CAL` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请假计算类型（WORK_DAY，NATURAL_DAY）',
  `SIGN_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '关联打卡记录编号',
  `COMPUTE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '计算方式',
  `CHANGE_SIGN_STATUS` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否修改打卡状态',
  `APPROVAL_STATUS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批进行状态（进行中，已完成，已关闭）',
  `OFFSET_TIME` datetime NULL DEFAULT NULL COMMENT '标记时间',
  `START_DATE` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `END_DATE` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `calculate_value` decimal(10, 2) NULL DEFAULT NULL COMMENT '与打卡记录取交集的计算时长',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_person_workflow_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_person_workflow_order_detail`;
CREATE TABLE `t_person_workflow_order_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NULL DEFAULT NULL,
  `EMP_ID` bigint(20) NOT NULL COMMENT '职员标识',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `LEAVE_ID` int(11) NULL DEFAULT NULL COMMENT '假期标识',
  `WORKFLOW_ORDER_ID` int(11) NOT NULL COMMENT '审批结果表编号',
  `VALUE` double(10, 2) NULL DEFAULT NULL COMMENT '时长',
  `DURATION` double(20, 2) NULL DEFAULT NULL COMMENT '时长（分钟数）',
  `ACTUAL_DURATION` double(20, 2) NULL DEFAULT NULL COMMENT '实际时长（分钟数）',
  `LEAVE_UNIT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请假单位（DAY、HALF_DAY、HOUR）',
  `LEAVE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批类型（请假、出差、外勤、补卡）',
  `DAY_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日期类型（WORKDAY,RESTDAY,HOLIDAY)',
  `COMPUTE_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '计算方式',
  `DETAIL_DATE` datetime NULL DEFAULT NULL COMMENT '审批明细日期',
  `START_DATE` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `END_DATE` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '审批日期明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sign_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sign_record`;
CREATE TABLE `t_sign_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `TAXSUB_ID` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `DEPT_ID` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `ATTEND_ID` bigint(20) NULL DEFAULT NULL COMMENT '考勤组id',
  `ATTEND_WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次id',
  `WORKING_SHIFT_ORDER` int(11) NULL DEFAULT NULL COMMENT '班次序号',
  `WORKING_SHIFT_DETAIL_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次明细',
  `EMP_ID` bigint(20) NOT NULL COMMENT '员工ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '员工姓名',
  `USER_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `WORK_DATE` datetime NULL DEFAULT NULL COMMENT '考勤日期',
  `SIGN_TIME` datetime NULL DEFAULT NULL COMMENT '打卡时间',
  `SIGN_TIME_UNCHECKED` datetime NULL DEFAULT NULL COMMENT '不考勤组打卡时间',
  `DURATION_MINUTES` int(11) NULL DEFAULT 0 COMMENT '时长',
  `ORIGIN_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '原纪录id',
  `REPLACE_RECORD_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更替类型',
  `ORIGIN_DURATION_MINUTES` int(11) NULL DEFAULT 0 COMMENT '原时长',
  `WORKING_DURATION_MINUTES` int(11) NULL DEFAULT 0 COMMENT '工作时长(分钟数)',
  `LEAVE_DURATION_MINUTES` int(11) NULL DEFAULT 0 COMMENT '请假时长(分钟数)',
  `OPERATOR_EMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '操作员id',
  `OPERATOR_EMP_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作员姓名',
  `SIGN_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型(上班=TO_WORK、下班=FROM_WORK)',
  `SIGN_DESCRIPTION` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡备注',
  `SIGN_IMAGES` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡图片(JSON数组)',
  `RECORD_ADDRESS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡地点',
  `LONGITUDE` decimal(32, 10) NULL DEFAULT NULL COMMENT '经度',
  `LATITUDE` decimal(32, 10) NULL DEFAULT NULL COMMENT '纬度',
  `DEVICE_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作设备类别：手机品牌|PAD|PC|其他',
  `DEVICE_OS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作设备类别：Mac|Windows|IOS|Android|其他',
  `DEVICE_IMEI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡设备识别号',
  `SIGN_WIFI_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'WIFI名称',
  `SIGN_WIFI_MAC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'WIFI-MAC',
  `IS_HOLIDAY` tinyint(4) NOT NULL COMMENT '是否法定节假日',
  `IS_WORKDAY` tinyint(4) NOT NULL COMMENT '是否调休工作日',
  `IS_SELF_HOLIDAYS` tinyint(4) NOT NULL COMMENT '是否休假',
  `IS_ABSENT` tinyint(4) NOT NULL COMMENT '是否缺勤',
  `IS_ABNORMAL_DUTY` tinyint(4) NOT NULL COMMENT '是否异常打卡',
  `IS_CHANGE_SHIFT` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否换班打卡',
  `IS_LACK_OF_TIME` tinyint(4) NOT NULL COMMENT '是否工时不足',
  `IS_WORK_OVERTIME` tinyint(4) NOT NULL COMMENT '是否加班',
  `IS_LEAVE_EARLIER` tinyint(4) NOT NULL COMMENT '是否早退',
  `IS_LATE` tinyint(4) NOT NULL COMMENT '是否迟到',
  `IS_WEEKEND` tinyint(4) NOT NULL COMMENT '是否周末',
  `NEED_APPROVE` tinyint(4) NULL DEFAULT NULL COMMENT '是否需要审批',
  `IS_EFFECTIVE` tinyint(4) NULL DEFAULT NULL COMMENT '打卡是否生效',
  `SIGN_RESULT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打卡结果',
  `ORIGIN_SIGN_RESULT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '原打卡结果',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `SALARY_MINUTES` int(11) NOT NULL DEFAULT 0 COMMENT '计薪时长(分钟)',
  `SIGN_TEMPERATURE` decimal(4, 1) NOT NULL DEFAULT 0.0 COMMENT '打卡时体温',
  `SIGN_TEMPERATURE_STATUS` tinyint(4) NOT NULL DEFAULT 0 COMMENT '体温状态：0-无；1-正常；2-异常',
  `SIGN_BY_FACE` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否人脸打卡：0-否，1-是',
  `SOURCE_RECORD_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '源数据（打卡机推送数据）id',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_COM_USER_ID_WORK_DATE`(`CO_ID`, `USER_ID`, `WORK_DATE`) USING BTREE,
  INDEX `IDX_EMP_WORK_DATE`(`EMP_ID`, `WORK_DATE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '员工打卡记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_special_day
-- ----------------------------
DROP TABLE IF EXISTS `t_special_day`;
CREATE TABLE `t_special_day`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `OVERTIME_RULE_ID` bigint(20) NOT NULL COMMENT '加班规则id',
  `OVERTIME_DETAIL_RULE_ID` bigint(20) NULL DEFAULT NULL COMMENT '加班规则详情id',
  `BEGIN_DATE` date NOT NULL COMMENT '开始日期',
  `END_DATE` date NOT NULL COMMENT '结束日期',
  `SPECIAL_DAY_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '节假日名称',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '自定义节假日' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_supplement_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_supplement_rule`;
CREATE TABLE `t_supplement_rule`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `RULE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '补卡规则名称',
  `ALLOW_SUPPLEMENT` tinyint(4) NOT NULL COMMENT '允许补卡',
  `ALLOW_TIMES_LIMIT` tinyint(4) NULL DEFAULT NULL COMMENT '允许补卡次数限制是否生效',
  `ALLOW_DATE_LIMIT` tinyint(4) NULL DEFAULT NULL COMMENT '允许补卡时间限制是否生效',
  `TIMES_LIMIT` int(11) NULL DEFAULT NULL COMMENT '补卡次数限制',
  `DATE_LIMIT` int(11) NULL DEFAULT NULL COMMENT '补卡时间限制',
  `IS_DEFAULT` tinyint(4) NULL DEFAULT 0 COMMENT '是否默认规则',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '补卡规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '持有者',
  `LOCKED_TIME` datetime NULL DEFAULT NULL COMMENT '加锁时间',
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_third_party_leave
-- ----------------------------
DROP TABLE IF EXISTS `t_third_party_leave`;
CREATE TABLE `t_third_party_leave`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `PLATFORM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '第三方类型  DING-钉钉',
  `LEAVE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '假期名称',
  `LEAVE_UNIT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '假期单位',
  `LEAVE_CODE` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '假期类型唯一标识',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_work_day_config
-- ----------------------------
DROP TABLE IF EXISTS `t_work_day_config`;
CREATE TABLE `t_work_day_config`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次id',
  `CONFIG_DATE` date NOT NULL COMMENT '配置日期',
  `CONFIG_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置类型：LEGAL_HOLIDAY,LEGAL_WORKDAY,SPECIAL_HOLIDAY,SPECIAL_WORKDAY',
  `DESCRIPTION` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置说明',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_WORK_DAY_CONFIG_CO_ATTEND`(`CO_ID`, `ATTEND_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作日节假日安排' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_work_plan_calendar
-- ----------------------------
DROP TABLE IF EXISTS `t_work_plan_calendar`;
CREATE TABLE `t_work_plan_calendar`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `WPC_ID` bigint(20) NULL DEFAULT NULL COMMENT '排班周期ID',
  `CYCLE_ORDER` int(11) NULL DEFAULT NULL COMMENT '排班序号',
  `EMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '员工ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL,
  `WORK_DAY` datetime NULL DEFAULT NULL COMMENT '排班日期',
  `WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次ID',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '排班表日历' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_work_plan_cycle
-- ----------------------------
DROP TABLE IF EXISTS `t_work_plan_cycle`;
CREATE TABLE `t_work_plan_cycle`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `CYCLE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '周期名称',
  `CYCLE_DAYS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '周期天数',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '排班周期' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_work_plan_cycle_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_work_plan_cycle_detail`;
CREATE TABLE `t_work_plan_cycle_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ATTEND_ID` bigint(20) NOT NULL COMMENT '考勤组id',
  `WPC_ID` bigint(20) NULL DEFAULT NULL COMMENT '排班周期ID',
  `CYCLE_ORDER` int(11) NULL DEFAULT NULL,
  `IS_REST_DAY` tinyint(4) NULL DEFAULT 0 COMMENT '是否为休息日',
  `WORKING_SHIFT_ID` bigint(20) NULL DEFAULT NULL COMMENT '班次ID',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '排班周期明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_working_shift
-- ----------------------------
DROP TABLE IF EXISTS `t_working_shift`;
CREATE TABLE `t_working_shift`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `GROUP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次组名称',
  `SHIFT_COUNT` int(11) NULL DEFAULT NULL COMMENT '班次数量',
  `SHIFT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '班次类型(FIX固定时间/FLEX弹性时间)',
  `WORKING_SWITCH` tinyint(4) NULL DEFAULT NULL COMMENT '时长调休(json数组)',
  `IS_DEFAULT` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是默认规则',
  `EFFECT_NOW` tinyint(4) NULL DEFAULT 1 COMMENT '是否立即生效',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `WORKING_MINUTES` int(11) NOT NULL DEFAULT 0 COMMENT '班次时长',
  `IS_UNCHECKED_SHIFT` tinyint(4) NULL DEFAULT NULL COMMENT '是否为不打卡班次',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '班次' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_working_shift_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_working_shift_detail`;
CREATE TABLE `t_working_shift_detail`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `WORKING_SHIFT_ID` bigint(20) NOT NULL COMMENT '班次id',
  `ORDER_NO` int(11) NOT NULL COMMENT '班次序号',
  `SHIFT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '班次类型(固定时间/弹性时间)',
  `NEED_CLOCK_IN` tinyint(4) NOT NULL DEFAULT 1 COMMENT '上班打卡',
  `WORKING_BEGIN` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标准上班时间',
  `WORKING_BEGIN_ADVANCE` int(11) NOT NULL DEFAULT 0 COMMENT '上班提前打卡时间(分钟数)',
  `WORKING_BEGIN_LATE` int(11) NOT NULL DEFAULT 0 COMMENT '上班迟到打卡时间(分钟数)',
  `ALLOW_BEGIN_FLEX` tinyint(4) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡规则是否生效',
  `WORKING_BEGIN_FLEX_HOUR` int(11) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡时间（小时数）',
  `WORKING_BEGIN_FLEX` int(11) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡时间(分钟数)',
  `WORKING_BEGIN_ABSENT` int(11) NOT NULL DEFAULT 0 COMMENT '上班缺卡打卡时间',
  `NEED_CLOCK_OUT` tinyint(4) NOT NULL DEFAULT 1 COMMENT '下班打卡',
  `WORKING_END` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标准下班时间',
  `WORKING_END_ADVANCE` int(11) NOT NULL DEFAULT 0 COMMENT '下班早退打卡时间(分钟数)',
  `WORKING_END_LATE` int(11) NOT NULL DEFAULT 0 COMMENT '下班延后打卡时间(分钟数)',
  `ALLOW_END_FLEX` tinyint(4) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡规则是否生效',
  `WORKING_END_FLEX_HOUR` int(11) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡时间（小时数）',
  `WORKING_END_FLEX` int(11) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡时间(分钟数)',
  `WORKING_END_ABSENT` int(11) NOT NULL DEFAULT 0 COMMENT '下班缺卡打卡时间',
  `WORKING_HOURS` int(11) NOT NULL DEFAULT 0 COMMENT '工作时长',
  `ALLOW_REST` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否开启休息时间段',
  `REST_BEGIN` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '休息开始时间',
  `REST_END` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '休息结束时间',
  `IS_DELETED` tinyint(4) NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `WORK_BEGIN_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TODAY' COMMENT '班次开始时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `WORK_END_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TODAY' COMMENT '班次结束时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `REST_BEGIN_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次休息开始时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `REST_END_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次休息结束时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '班次明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_working_shift_detail_tomorrow
-- ----------------------------
DROP TABLE IF EXISTS `t_working_shift_detail_tomorrow`;
CREATE TABLE `t_working_shift_detail_tomorrow`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `WORKING_SHIFT_TOMORROW_ID` bigint(20) NOT NULL COMMENT '明日生效班次id',
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `ORDER_NO` int(11) NOT NULL COMMENT '班次序号',
  `SHIFT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '班次类型(固定时间/弹性时间)',
  `NEED_CLOCK_IN` tinyint(4) NOT NULL DEFAULT 1 COMMENT '上班打卡',
  `WORKING_BEGIN` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标准上班时间',
  `WORKING_BEGIN_ADVANCE` int(11) NOT NULL DEFAULT 0 COMMENT '上班提前打卡时间(分钟数)',
  `WORKING_BEGIN_LATE` int(11) NOT NULL DEFAULT 0 COMMENT '上班迟到打卡时间(分钟数)',
  `ALLOW_BEGIN_FLEX` tinyint(4) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡规则是否生效',
  `WORKING_BEGIN_FLEX_HOUR` int(11) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡时间（小时数）',
  `WORKING_BEGIN_FLEX` int(11) NOT NULL DEFAULT 0 COMMENT '上班弹性打卡时间(分钟数)',
  `WORKING_BEGIN_ABSENT` int(11) NOT NULL DEFAULT 0 COMMENT '上班缺卡打卡时间',
  `NEED_CLOCK_OUT` tinyint(4) NOT NULL DEFAULT 1 COMMENT '下班打卡',
  `WORKING_END` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标准下班时间',
  `WORKING_END_ADVANCE` int(11) NOT NULL DEFAULT 0 COMMENT '下班早退打卡时间(分钟数)',
  `WORKING_END_LATE` int(11) NOT NULL DEFAULT 0 COMMENT '下班延后打卡时间(分钟数)',
  `ALLOW_END_FLEX` tinyint(4) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡规则是否生效',
  `WORKING_END_FLEX_HOUR` int(11) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡时间（小时数）',
  `WORKING_END_FLEX` int(11) NOT NULL DEFAULT 0 COMMENT '下班弹性打卡时间(分钟数)',
  `WORKING_END_ABSENT` int(11) NOT NULL DEFAULT 0 COMMENT '下班缺卡打卡时间',
  `WORKING_HOURS` int(11) NOT NULL DEFAULT 0 COMMENT '工作时长',
  `ALLOW_REST` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否开启休息时间段',
  `REST_BEGIN` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '休息开始时间',
  `REST_END` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '休息结束时间',
  `IS_DELETED` tinyint(4) NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `WORK_BEGIN_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TODAY' COMMENT '班次开始时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `WORK_END_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TODAY' COMMENT '班次结束时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `REST_BEGIN_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次休息开始时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  `REST_END_OVER_DAY_FLAG` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次休息结束时间是否跨天标识字段，TODAY-当日，NEXT_DAY-次日',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '班次明细明日生效表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_working_shift_tomorrow
-- ----------------------------
DROP TABLE IF EXISTS `t_working_shift_tomorrow`;
CREATE TABLE `t_working_shift_tomorrow`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `REAL_WORKING_SHIFT_ID` bigint(20) NOT NULL COMMENT '原班次ID',
  `CO_ID` bigint(20) NOT NULL COMMENT '企业id',
  `GROUP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '班次组名称',
  `SHIFT_COUNT` int(11) NULL DEFAULT NULL COMMENT '班次数量',
  `SHIFT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '班次类型(FIX固定时间/FLEX弹性时间)',
  `WORKING_SWITCH` tinyint(4) NULL DEFAULT NULL COMMENT '时长调休(json数组)',
  `IS_DEFAULT` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是默认规则',
  `IS_DELETED` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  `CREATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `WORKING_MINUTES` int(11) NOT NULL DEFAULT 0 COMMENT '班次时长',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '班次明日生效表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Foreign Keys structure for table qrtz_blob_triggers
-- ----------------------------
ALTER TABLE `qrtz_blob_triggers` ADD CONSTRAINT `qrtz_blob_triggers_OBFK_1749179620916981` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_cron_triggers
-- ----------------------------
ALTER TABLE `qrtz_cron_triggers` ADD CONSTRAINT `qrtz_cron_triggers_OBFK_1749179620668366` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_simple_triggers
-- ----------------------------
ALTER TABLE `qrtz_simple_triggers` ADD CONSTRAINT `qrtz_simple_triggers_OBFK_1749179620548846` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_simprop_triggers
-- ----------------------------
ALTER TABLE `qrtz_simprop_triggers` ADD CONSTRAINT `qrtz_simprop_triggers_OBFK_1749179620791872` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Foreign Keys structure for table qrtz_triggers
-- ----------------------------
ALTER TABLE `qrtz_triggers` ADD CONSTRAINT `qrtz_triggers_OBFK_1749179620397674` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `qrtz_job_details` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT;

SET FOREIGN_KEY_CHECKS = 1;
