/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : payroll

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:28:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_bank_info
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info`;
CREATE TABLE `t_bank_info`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `BANK_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行号',
  `BANK_NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行名',
  `BANK_ALIAS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行别名',
  `BANK_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行代码',
  `BANK_ORG_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构类型',
  `BANK_AGENCY_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代理清算行号',
  `BANK_RECKON_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清算行号',
  `BANK_INURE_DATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生效日期',
  `BANK_TYPE_UPDATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更起始日',
  `BANK_UPDATE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更类型',
  `BANK_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `BANK_LOGON_STATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录状态',
  `CREATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行行名行号对应表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info1
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info1`;
CREATE TABLE `t_bank_info1`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `BANK_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行号',
  `BANK_NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行名',
  `BANK_ALIAS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行别名',
  `BANK_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行代码',
  `BANK_ORG_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构类型',
  `BANK_AGENCY_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代理清算行号',
  `BANK_RECKON_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清算行号',
  `BANK_INURE_DATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生效日期',
  `BANK_TYPE_UPDATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更起始日',
  `BANK_UPDATE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更类型',
  `BANK_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `BANK_LOGON_STATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录状态',
  `CREATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行行名行号对应表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_info2
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_info2`;
CREATE TABLE `t_bank_info2`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `BANK_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行号',
  `BANK_NAME` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行名',
  `BANK_ALIAS` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行别名',
  `BANK_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行代码',
  `BANK_ORG_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构类型',
  `BANK_AGENCY_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代理清算行号',
  `BANK_RECKON_BANK` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清算行号',
  `BANK_INURE_DATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生效日期',
  `BANK_TYPE_UPDATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更起始日',
  `BANK_UPDATE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '变更类型',
  `BANK_STATUS` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `BANK_LOGON_STATE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录状态',
  `CREATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行行名行号对应表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_boc_pay_file
-- ----------------------------
DROP TABLE IF EXISTS `t_boc_pay_file`;
CREATE TABLE `t_boc_pay_file`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PAY_BATCH_ID` int(11) NOT NULL COMMENT '代发id',
  `TOTAL_FILE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `DETAIL_FILE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZIP_FILE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RESP_FILE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CREATE_TIME` datetime NULL DEFAULT NULL,
  `RETURN_TIME` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_boc_sms
-- ----------------------------
DROP TABLE IF EXISTS `t_boc_sms`;
CREATE TABLE `t_boc_sms`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PAY_BATCH_ID` bigint(20) NULL DEFAULT NULL,
  `SMS_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERIFY_TIME` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_pay_batch_id`(`PAY_BATCH_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_electronic_account
-- ----------------------------
DROP TABLE IF EXISTS `t_electronic_account`;
CREATE TABLE `t_electronic_account`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL,
  `BANK_CODE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `TAXSUB_ID` bigint(20) NULL DEFAULT NULL,
  `TAXPAYER_NO` varchar(49) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCOUNT_BANK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ACCOUNT_BANK_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `HANDLER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `HANDLER_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_field_mapping
-- ----------------------------
DROP TABLE IF EXISTS `t_field_mapping`;
CREATE TABLE `t_field_mapping`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '公司ID',
  `SYSTEM_FIELD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '系统字段',
  `MAPPING_FIELD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '映射字段',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工资条-系统匹配字段映射关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_channel_conf
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_channel_conf`;
CREATE TABLE `t_pay_channel_conf`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CHANNEL_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道名称',
  `CHANNEL_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道编码',
  `CHANNEL_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道类型',
  `DOWNLOAD_RECEIPT_YN` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持下载回单',
  `INTERBANK_YN` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持跨行',
  `CHANNEL_DESC` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道描述',
  `ENBALE_YN` tinyint(4) NOT NULL COMMENT '启用状态',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `LINK_ADDRESS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PICTURE_GUIDE_LINK_ADDRESS` int(11) NULL DEFAULT NULL,
  `VIDEO_GUIDE_LINK_ADDRESS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BANK_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属银行编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '代发通道配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_export_record
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_export_record`;
CREATE TABLE `t_pay_export_record`  (
  `id` bigint(20) NOT NULL,
  `COMP_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `PAY_BATCH_ID` bigint(20) NULL DEFAULT NULL COMMENT '批次ID',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纳税识别号',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `CHANNEL_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发通道',
  `BATCH_ENCRYPT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'MD5',
  `HANDLER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '经办人',
  `HANDLER_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '经办人手机号',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '发放文件导出记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_salary_apply_batch
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_salary_apply_batch`;
CREATE TABLE `t_pay_salary_apply_batch`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称 记录当时的公司名称',
  `SALARY_MONTH` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资月份',
  `SALARY_RULE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资表名称',
  `SALARY_CHECK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '薪资表id',
  `TOTAL_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '总笔数',
  `TOTAL_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '总金额',
  `CHECK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '校验状态',
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发状态',
  `ERROR_INFO` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `UPLOAD_FILE_ID` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `FIELD_MAPPING` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `PAY_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '代发账户主键id',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '代发申请批次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_salary_apply_record
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_salary_apply_record`;
CREATE TABLE `t_pay_salary_apply_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `APPLY_BATCH_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '批次id',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号 冗余字段-统计用',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称 冗余字段-统计用',
  `SALARY_MONTH` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '月份 冗余字段-统计员工数据用',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `MOBILE_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `ACCOUNT_BANK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发卡银行',
  `ACCOUNT_BANK_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行卡号',
  `REAL_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '实发金额',
  `CHECK_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '校验状态',
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发状态',
  `ERROR_INFO` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `ACC_UNION_BANK_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联行号',
  `POST_SCRIPT` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附言',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '代发申请批次明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_salary_batch
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_salary_batch`;
CREATE TABLE `t_pay_salary_batch`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称',
  `CHANNEL_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发通道',
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发状态',
  `TOTAL_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '总笔数',
  `SUCCESS_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '成功笔数',
  `FAIL_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '失败笔数',
  `TOTAL_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '总金额',
  `SUCCESS_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '成功金额',
  `FAIL_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '失败金额',
  `ERROR_INFO` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `BATCH_ENCRYPT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '批次MD5',
  `APPLY_BATCH_IDS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申请批次id列表',
  `USER_PHONE` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发发放人手机号',
  `APPOINTMENT_YN` tinyint(4) NULL DEFAULT NULL COMMENT '是否预约发放',
  `APPOINTMENT_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预约发放时间',
  `PAY_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '付款时间',
  `COMPLETE_TIME` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `PAY_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '代发账户主键id',
  `BATCH_NUMBER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发批次号',
  `SUBMIT_ORDER_TIME` timestamp NULL DEFAULT NULL COMMENT '开始代发付款批次时间',
  `SALARY_MONTH` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '薪资月份',
  `OBS_URL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'OBS文件路径',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '代发批次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_salary_record
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_salary_record`;
CREATE TABLE `t_pay_salary_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NULL DEFAULT NULL COMMENT '企业ID',
  `PAY_BATCH_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '代发批次id',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号 冗余字段',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称 冗余字段',
  `SALARY_MONTH` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工资月份(yyyyMM)',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `MOBILE_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `ACCOUNT_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发卡银行',
  `ACCOUNT_BANK_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行卡号',
  `REAL_AMOUNT` decimal(32, 8) NULL DEFAULT NULL COMMENT '实发金额',
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '代发状态',
  `APPLY_BATCH_ID` bigint(20) NULL DEFAULT NULL COMMENT '申请批次id 回写申请状态时使用',
  `APPLY_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '申请明细id',
  `ERROR_INFO` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `ACC_UNION_BANK_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联行号',
  `POST_SCRIPT` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附言',
  `FILE_NUMBER` int(20) NULL DEFAULT NULL COMMENT '文件序号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '代发批次明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_subject_account
-- ----------------------------
DROP TABLE IF EXISTS `t_subject_account`;
CREATE TABLE `t_subject_account`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `COMP_ID` bigint(20) NOT NULL COMMENT '企业ID',
  `UNIFIED_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统一社会信用代码',
  `SUBJECT_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公司名称',
  `ACCOUNT_BANK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开户行名称',
  `ACCOUNT_BANK_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开户账号',
  `BASE_ACCOUNT_BANK_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '基本户账户',
  `BASE_ACCOUNT_BANK` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '基本户开户行',
  `HANDLER` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经办人',
  `HANDLER_MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经办人手机号',
  `CHANNEL_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '通道编码',
  `ENBALE_YN` tinyint(4) NOT NULL COMMENT '启用状态',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '乐观锁',
  `CREATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `SEND_MESSAGE_YN` tinyint(1) NOT NULL DEFAULT 0,
  `ACCOUNT_STATUS` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建代发账户时对应行内账户状态--财资（0:销户,1:正常,2:部分冻结,3:只收不付,4:只付不收,5:全部冻结）',
  `CORE_CUSTOMER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '核心客户号',
  `TREASURY_CUSTOMER_NO` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '财资客户号',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '主体账户管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_subject_account_open_card
-- ----------------------------
DROP TABLE IF EXISTS `t_subject_account_open_card`;
CREATE TABLE `t_subject_account_open_card`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SUBJECT_ACCOUNT_ID` bigint(20) NULL DEFAULT NULL COMMENT '代发账户id',
  `FILE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '批量开户二维码文件id',
  `FILE_URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '批量开户二维码文件url',
  `OPEN_CARD_URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '批量开卡url',
  `NOTIFY_FLAG` int(11) NULL DEFAULT NULL COMMENT '是否已通知员工',
  `GENERATE_TIME` datetime NULL DEFAULT NULL COMMENT '二维码生成日期',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '批量开卡账户二维码信息映射表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_subject_account_open_card_user
-- ----------------------------
DROP TABLE IF EXISTS `t_subject_account_open_card_user`;
CREATE TABLE `t_subject_account_open_card_user`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `OPEN_CARD_ID` bigint(20) NULL DEFAULT NULL COMMENT '批量开卡ID',
  `USER_ID` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `SEND_FLAG` int(11) NULL DEFAULT NULL COMMENT '是否发送消息',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_OPEN_CARD_ID`(`OPEN_CARD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '批量开卡二维码信息用户映射表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_amqp_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_amqp_msg`;
CREATE TABLE `t_sys_amqp_msg`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `EXCHANGE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  `CREATE_TIME` datetime NOT NULL,
  `COMPLETE_TIME` datetime NULL DEFAULT NULL,
  `FAIL_COUNT` int(11) NULL DEFAULT NULL,
  `LAST_FAIL_TIME` datetime NULL DEFAULT NULL,
  `PROPERTIES` blob NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '持有者',
  `LOCKED_TIME` datetime NULL DEFAULT NULL COMMENT '加锁时间',
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
