/*
 Navicat Premium Data Transfer

 Source Server         : hrsaas@hrsaas#OBV421_CS_01@-************
 Source Server Type    : MySQL
 Source Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 Source Host           : ************:3306
 Source Schema         : subtax

 Target Server Type    : MySQL
 Target Server Version : 50799 (OceanBase 5.7.25-OceanBase-v4.2.1.10)
 File Encoding         : 65001

 Date: 06/06/2025 16:37:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_add_deduct_back
-- ----------------------------
DROP TABLE IF EXISTS `t_add_deduct_back`;
CREATE TABLE `t_add_deduct_back`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `NSRXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人姓名',
  `NSRZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人证件类型',
  `NSRZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人证照号码',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '申报标志',
  `BSND` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报送年度',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈信息',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主表错误信息',
  `DEDUCT_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣除类型，自定义的枚举，分辩是子女教育，还是房屋扣除',
  `GXSJ` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求id',
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号',
  `SFDSZN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否独生子女',
  `FTFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '赡养分摊方式',
  `BNDYKCJE` decimal(10, 4) NULL DEFAULT NULL COMMENT '本年度月扣除金额',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_ADD_DEDUCT_BACK_REQUEST_RECORD_ID_index`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '专项附加扣除反馈信息主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_payment_cancel
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_payment_cancel`;
CREATE TABLE `t_bank_payment_cancel`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `BBLB` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `STATUS` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '作废处理状态 -9：未处理；-1：作废失败；0：作废成功',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BANKCANCEL_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE,
  INDEX `IDX_MER_REQUEST_NO`(`MERCHANT_ID`, `REQUEST_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行缴款书作废' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_bank_payment_file
-- ----------------------------
DROP TABLE IF EXISTS `t_bank_payment_file`;
CREATE TABLE `t_bank_payment_file`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `INCOME_MONTH` date NULL DEFAULT NULL COMMENT '所得月份',
  `SBZL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报种类',
  `SBLB` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报类别',
  `NSRS` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '纳税人数',
  `SRZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '收入总额',
  `YKJSE` decimal(10, 2) NULL DEFAULT NULL COMMENT '应扣缴税额',
  `DYZT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打印状态',
  `DYSJ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '打印时间',
  `JKPZSE` decimal(10, 2) NULL DEFAULT NULL COMMENT '缴款凭证税额',
  `FILE_NM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款书文件名称',
  `FILE` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款书文件',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_BANKFILE_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE,
  INDEX `IDX_MER_REQUEST_NO`(`MERCHANT_ID`, `REQUEST_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '银行缴款书' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_calculate_request_info
-- ----------------------------
DROP TABLE IF EXISTS `t_calculate_request_info`;
CREATE TABLE `t_calculate_request_info`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '请求记录表ID',
  `REPORT_FROM_TYPE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '来源类型',
  `QKDYSJ` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否清空当前所得月份非居民所得的全量数据',
  `AUTOFILLLZBCFB` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否自动填充离职补偿附表',
  `QKSDXMLB` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清空所得项目列表',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_CAL_REQUEST_INFOID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_child_edu
-- ----------------------------
DROP TABLE IF EXISTS `t_child_edu`;
CREATE TABLE `t_child_edu`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '申报反馈的主表id',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '明细申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报反馈信息',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '采集来源',
  `GX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关系',
  `ZNXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子女姓名',
  `ZNZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子女证照类型',
  `ZNZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子女证照号码',
  `ZNCSNY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子女出生年月',
  `ZNGJDQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '子女国籍',
  `SJYJD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '受教育阶段',
  `SJYRQQ` date NULL DEFAULT NULL COMMENT '受教育日期起',
  `SJYRQZ` date NULL DEFAULT NULL COMMENT '受教育日期止',
  `JYZZRQ` date NULL DEFAULT NULL COMMENT '教育终止日期',
  `JDGJDQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '就读国籍地区',
  `JDXX` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '就读学校',
  `FPBL` int(11) NULL DEFAULT NULL COMMENT '分配比例',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `GXSJ` date NULL DEFAULT NULL,
  `KCYXQQ` date NULL DEFAULT NULL,
  `KCYXQZ` date NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_CHILD_EDU_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '子女教育' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_continue_edu
-- ----------------------------
DROP TABLE IF EXISTS `t_continue_edu`;
CREATE TABLE `t_continue_edu`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '明细申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细申报反馈',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '采集来源',
  `JXJYLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '教育阶段',
  `ZSQDSJ` date NULL DEFAULT NULL COMMENT '证书发放时间',
  `ZSMC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书名称',
  `ZSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书编号',
  `FZJG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发证机关',
  `JYJD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学历教育-教育阶段',
  `RXSJ` date NULL DEFAULT NULL COMMENT '入学时间',
  `BYSJ` date NULL DEFAULT NULL COMMENT '预计毕业时间',
  `FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '非学历教育/学历教育标识字段',
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '专项申报反馈主表id',
  `GXSJ` date NULL DEFAULT NULL,
  `KCYXQQ` date NULL DEFAULT NULL,
  `KCYXQZ` date NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_CONTINUE_EDU_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '继续教育' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_focus_confirm_record
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_focus_confirm_record`;
CREATE TABLE `t_emp_focus_confirm_record`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REQUEST_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '请求id',
  `REQUEST_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0,
  `MERCHANT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '商户号',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名字',
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍',
  `DEDUCTION_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否扣除6万元减除费用',
  `CONFIRM_STATUS` int(11) NULL DEFAULT NULL COMMENT '确认状态',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_EMP_FOCUS_CONFIRM_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_focus_info
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_focus_info`;
CREATE TABLE `t_emp_focus_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REQUEST_RECORD_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `MERCHANT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '商户号',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证件号码',
  `EMP_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍',
  `DEDUCTION_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否扣除6万元减除费用',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_EMP_FOCUS_INFO_ID_NO`(`REQUEST_RECORD_ID`, `MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_report_info
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_report_info`;
CREATE TABLE `t_emp_report_info`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍（地区）',
  `XB` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '性别：1 男, 2 女',
  `CSNY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出生日期',
  `EMP_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员状态：1 正常，2 非正常',
  `SFKCJCFY` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否扣除减除费用',
  `XL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学历',
  `SFCJ` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否残疾',
  `SFLS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否烈属',
  `SFGL` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否孤老',
  `CJZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '残疾证号',
  `LSZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '烈属证号',
  `BZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `LXDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_省',
  `LXDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_市',
  `LXDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_区县',
  `LXDZ_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_街道',
  `LXDZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_详细',
  `HJSZD_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_省',
  `HJSZD_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_市',
  `HJSZD_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_区县',
  `HJSZD_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_街道',
  `HJSZD_XXDZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_详细',
  `MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号码',
  `DZYX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子邮箱',
  `KHYH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户银行:非必填',
  `KHYH_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户银行省份',
  `YHZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行账号',
  `WORKER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任职受雇从业类型',
  `RZNDJYQK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任职年度就业情况',
  `EMP_DAY` date NULL DEFAULT NULL COMMENT '任职受雇日期',
  `LEAVE_DAY` date NULL DEFAULT NULL COMMENT '离职日期',
  `SFBD` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否并档',
  `GRGBZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '个人投资总额:为鼓动投资者时必填',
  `ZW` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '职务',
  `GRTZBL` decimal(10, 2) NULL DEFAULT NULL COMMENT '个人投资比例',
  `GH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工号',
  `XMZW` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '中文名',
  `QTZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '其他证照类型',
  `QTZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '其他证照号码',
  `CSD` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出生国家',
  `SSSY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '涉税事由',
  `SCRJSJ` datetime NULL DEFAULT NULL COMMENT '首次入境时间',
  `YJLJSJ` datetime NULL DEFAULT NULL COMMENT '预计离境时间',
  `WJRLXDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_省',
  `WJRLXDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_市',
  `WJRLXDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_区县',
  `WJRLXDZ_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_街道',
  `WJRLXDZ_XXDZ` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_详细',
  `CHANNEL_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道:SERVYOU-税友接口,SELF_SERVYOU-自有模式',
  `PERSON_REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人登记序号',
  `NATURAL_PERSON_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人档案号',
  `PERSON_PAYER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人纳税人识别号',
  `DATA_INPUT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员数据入库状态',
  `REPORT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员报送状态',
  `VERIFY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员认证状态',
  `FAIL_REASON` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '失败原因',
  `SERVYOU_REPORT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税友报送状态',
  `SELF_REPORT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自由通道报送状态',
  `SW_WORKER_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主键',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `CJZJLX` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '残疾证件类型',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_EMP_INFO_ID_NO`(`TAX_PAYER_NO`, `ID_NO`) USING BTREE,
  INDEX `IDX_EMP_INFO_MERCHANT`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '人员报送信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_emp_report_record
-- ----------------------------
DROP TABLE IF EXISTS `t_emp_report_record`;
CREATE TABLE `t_emp_report_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `REQUEST_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `DJXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人登记序号',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `COUNTRY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍（地区）',
  `XB` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '性别：1 男, 2 女',
  `CSNY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出生日期',
  `EMP_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员状态：1 正常，2 非正常',
  `SFKCJCFY` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否扣除减除费用',
  `XL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '学历',
  `SFCJ` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否残疾',
  `SFLS` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否烈属',
  `SFGL` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否孤老',
  `CJZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '残疾证号',
  `LSZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '烈属证号',
  `BZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `LXDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_省',
  `LXDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_市',
  `LXDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_区县',
  `LXDZ_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_街道',
  `LXDZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经常居住地_详细',
  `HJSZD_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_省',
  `HJSZD_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_市',
  `HJSZD_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_区县',
  `HJSZD_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_街道',
  `HJSZD_XXDZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '户籍地址_详细',
  `MOBILE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号码',
  `DZYX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子邮箱',
  `ACCOUNT_BANK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户银行:非必填',
  `ACCOUNT_BANK_PROVINCE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户银行省份',
  `ACCOUNT_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行账号',
  `WORKER_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任职受雇从业类型',
  `RZNDJYQK` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '任职年度就业情况',
  `RZSGRQ` date NULL DEFAULT NULL COMMENT '任职受雇日期',
  `LZRQ` date NULL DEFAULT NULL COMMENT '离职日期',
  `ISARCHIVE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否并档',
  `ARCHIVE_RESULT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '并档结果',
  `GRGBZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '个人投资总额:为鼓动投资者时必填',
  `ZW` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '职务',
  `GRTZBL` decimal(10, 2) NULL DEFAULT NULL COMMENT '个人投资比例',
  `GH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工号',
  `XMZW` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '中文名',
  `QTZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '其他证照类型',
  `QTZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '其他证照号码',
  `CSD` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出生国家',
  `SSSY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '涉税事由',
  `SCRJSJ` datetime NULL DEFAULT NULL COMMENT '首次入境时间',
  `YJLJSJ` datetime NULL DEFAULT NULL COMMENT '预计离境时间',
  `WJRLXDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_省',
  `WJRLXDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_市',
  `WJRLXDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_区县',
  `WJRLXDZ_JD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_街道',
  `WJRLXDZ_XXDZ` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址_详细',
  `PERSON_REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人登记序号',
  `NATURAL_PERSON_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人档案号',
  `PERSON_PAYER_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自然人纳税人识别号',
  `DATA_INPUT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员数据入库状态',
  `REPORT_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员报送状态',
  `VERIFY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '人员认证状态',
  `FAIL_REASON` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '失败原因',
  `ACCOUNT_VERIFY_STATUS` int(11) NULL DEFAULT NULL COMMENT '银行账号验证状态',
  `ACCOUNT_VERIFY_INFO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行账号验证信息',
  `zgswjmc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务机关名称',
  `djzcmc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登记注册名称',
  `lxdh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系电话',
  `djrq` date NULL DEFAULT NULL COMMENT '登记日期',
  `BUSINESS_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务类型',
  `CHANNEL_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道:SERVYOU-税友接口,SELF_SERVYOU-自有模式',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `CJZJLX` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '残疾证件类型',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_EMP_RECORD_IDNO`(`REQUEST_RECORD_ID`, `ID_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '人员报送明细记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_family
-- ----------------------------
DROP TABLE IF EXISTS `t_family`;
CREATE TABLE `t_family`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `XM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `SFYPO` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有配偶',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求id',
  `MERCHANT_ID` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号',
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '专项申报反馈主表id',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_FAMILY_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_family_member
-- ----------------------------
DROP TABLE IF EXISTS `t_family_member`;
CREATE TABLE `t_family_member`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `XM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `GJ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国籍（地区）',
  `RZZT` int(11) NULL DEFAULT NULL COMMENT '身份验证状态',
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `FAMILY_ID` int(10) UNSIGNED NOT NULL COMMENT '家庭信息id',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_FAMILY_MEMBER_FAMILY_ID_index`(`FAMILY_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_house_loans
-- ----------------------------
DROP TABLE IF EXISTS `t_house_loans`;
CREATE TABLE `t_house_loans`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '明细申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细申报反馈信息',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '采集来源',
  `FWZLDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（省）',
  `FWZLDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（市）',
  `FWZLDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（县）',
  `FWZLDZ_JD` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（街道）',
  `FWZLDZ_XXDZ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址(详细地址)',
  `FWZSLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'int房屋证书类型',
  `FWZSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋证书编号',
  `SFHQST` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否婚前首套',
  `SFJKR` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否借款人',
  `FPBL` int(11) NULL DEFAULT NULL COMMENT '分配比例',
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '专项申报反馈主表id',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_HOUSE_LOANS_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '房屋贷款' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_house_loans_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_house_loans_detail`;
CREATE TABLE `t_house_loans_detail`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公积金/商业贷款 明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '公积金商业贷 申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公积金，商业贷申报反馈信息',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公积金，商业贷采集来源',
  `ISHASDKXX` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '判断是否填写贷款信息',
  `DKFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '贷款类型',
  `JRJGMC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '贷款银行',
  `DKHTBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '贷款合同编号',
  `SCHKRQ` date NULL DEFAULT NULL COMMENT '首次还款日期',
  `DKQX` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '贷款期限(月数)',
  `DKJSRQ` date NULL DEFAULT NULL COMMENT '贷款结束日期',
  `DKJE` decimal(10, 4) NULL DEFAULT NULL COMMENT '贷款金额（万元）',
  `FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公积金/商业贷款 标识字段',
  `T_HOUSE_LOANS_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '房屋贷款id',
  `GXSJ` date NULL DEFAULT NULL,
  `KCYXQQ` date NULL DEFAULT NULL,
  `KCYXQZ` date NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_HOUSE_LOANS_DETAIL_T_HOUSE_LOANS_ID_index`(`T_HOUSE_LOANS_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '公积金/商业贷款明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_house_rents
-- ----------------------------
DROP TABLE IF EXISTS `t_house_rents`;
CREATE TABLE `t_house_rents`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '明细申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报反馈信息详情',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '采集来源',
  `CZFLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出租房类型',
  `CZFTYSHXYDM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出租方统一社会信用代码',
  `CZFMC` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出租方名称',
  `FWZSLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋证书类型',
  `ZSHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书号码',
  `ZFZLHTBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租赁合同编号',
  `FWZLDZ_SHENG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（省）',
  `FWZLDZ_SHI` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（市）',
  `FWZLDZ_QX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（县）',
  `FWXLDZ_JD` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址（街道）',
  `FWXXDZ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '房屋坐落地址(详细地址)',
  `ZLRQQ` date NULL DEFAULT NULL COMMENT '租赁日期起',
  `ZLRQZ` date NULL DEFAULT NULL COMMENT '租赁日期止',
  `ZJJE` decimal(10, 4) NULL DEFAULT NULL COMMENT '租金金额',
  `GZCS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作城市',
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '专项申报反馈主表id',
  `GXSJ` date NULL DEFAULT NULL,
  `KCYXQQ` date NULL DEFAULT NULL,
  `KCYXQZ` date NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_HOUSE_RENTS_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '房屋租赁' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_jz_reduction_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_jz_reduction_detail`;
CREATE TABLE `t_jz_reduction_detail`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `REDUCTION_ITEM_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '捐赠扣除项id',
  `SZDWMC` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '受赠单位名称',
  `SZDWNSRSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '受赠单位纳税人识别号',
  `JZPZH` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '捐赠凭证号',
  `JZRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '捐赠日期',
  `JZJE` decimal(10, 2) NULL DEFAULT NULL COMMENT '捐赠金额',
  `KCBL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣除比例',
  `SJJZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际捐赠额',
  `BZ` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '算税状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_JZ_REDUCTION_ITEMID`(`REDUCTION_ITEM_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '捐赠扣除明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_last_reduction_item
-- ----------------------------
DROP TABLE IF EXISTS `t_last_reduction_item`;
CREATE TABLE `t_last_reduction_item`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `REDUCTION_ITEM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免事项、商业健康保险、税延养老保险',
  `NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `JMFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免方式',
  `SDXMDM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目代码',
  `JMSX` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免事项',
  `JMXZ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免性质',
  `JMSE` decimal(10, 2) NULL DEFAULT NULL COMMENT '减免税额/免税收入',
  `SYSBM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税友识别码',
  `BDSXRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '保单生效日期',
  `NDBF` decimal(10, 2) NULL DEFAULT NULL COMMENT '年度保费',
  `YDBF` decimal(10, 2) NULL DEFAULT NULL COMMENT '月度保费',
  `BQKCJE` decimal(10, 2) NULL DEFAULT NULL COMMENT '本期扣除金额',
  `SYYLZHBH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税延养老账号编号',
  `SBKCYF_START` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报扣除月份起',
  `SBKCYF_END` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报扣除月份止',
  `BSJYM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报税校验码',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '算税状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '减免事项' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_last_tax_calculate_result
-- ----------------------------
DROP TABLE IF EXISTS `t_last_tax_calculate_result`;
CREATE TABLE `t_last_tax_calculate_result`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `REPORT_FROM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别 预扣预缴、分类所得、非居民所得',
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目 工资薪金、劳务报酬、年终奖。。。',
  `DELETE_HISTORY_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否删除历史数据',
  `SRE` decimal(18, 2) NULL DEFAULT NULL COMMENT '当期收入额 全年一次性奖金：全年一次性奖金收入额解除劳务合同：一次性补偿',
  `MSSD` decimal(18, 2) NULL DEFAULT NULL COMMENT '当期免税收入',
  `ZYKCJZE` decimal(18, 2) NULL DEFAULT NULL COMMENT '准予扣除捐赠额',
  `JBYLAOBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '基本养老保险',
  `JBYLBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '基本医疗保险',
  `SYBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '失业保险',
  `ZFGJJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房公积金',
  `ZNJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '子女教育支出',
  `SYLRZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '赡养老人支出',
  `ZFDKLXZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房贷款利息支出',
  `ZFZJZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房租金支出',
  `DBYLZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '大病医疗支出',
  `JXJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '继续教育支出',
  `YYEZHFZC` decimal(18, 2) NULL DEFAULT NULL,
  `NJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '年金',
  `SYJKBX` decimal(18, 2) NULL DEFAULT NULL COMMENT '商业健康保险',
  `SYYLBX` decimal(18, 2) NULL DEFAULT NULL COMMENT '税延养老保险',
  `QT` decimal(18, 2) NULL DEFAULT NULL COMMENT '其他',
  `YXKCSF` decimal(18, 2) NULL DEFAULT NULL COMMENT '允许扣除的税费',
  `JMSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '减免税额',
  `LJSRE` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计收入',
  `LJFY` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计费用 劳务报酬（保险，证券）',
  `LJMSSR` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计免税收入',
  `JCFY` decimal(18, 2) NULL DEFAULT NULL COMMENT '劳务报酬为：减除费用',
  `ZXKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项扣除合计',
  `QTCKHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计其他扣除',
  `LJZYKCDJZE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计准予扣除的捐赠额',
  `ZXFJKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项附加扣除合计',
  `KCXMHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '扣除项目合计',
  `FY` decimal(18, 2) NULL DEFAULT NULL COMMENT '费用',
  `ZYCB` decimal(18, 2) NULL DEFAULT NULL COMMENT '展业成本',
  `LJYNSSDE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应纳税所得额',
  `SL` decimal(18, 4) NULL DEFAULT NULL COMMENT '税率',
  `SSKCS` decimal(18, 2) NULL DEFAULT NULL COMMENT '速算扣除数',
  `LJYNSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应纳税额',
  `LJJMSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计减免税额',
  `LJYINGKJSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应扣缴税额',
  `YKJSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '已预交税额',
  `YBTSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '应补退税额',
  `CALCULATE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '算税状态',
  `SDXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目代码',
  `SJJZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际捐赠额',
  `JZFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '捐赠方式',
  `BZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `SYGS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '适用公式',
  `JNTS` decimal(10, 2) NULL DEFAULT NULL COMMENT '境内工作天数',
  `JWTS` decimal(10, 2) NULL DEFAULT NULL COMMENT '境外工作天数',
  `JNZF` decimal(10, 2) NULL DEFAULT NULL COMMENT '境内支付金额',
  `JWZF` decimal(10, 2) NULL DEFAULT NULL COMMENT '境外支付金额',
  `LJJCFY` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计减除费用',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  `REQUEST_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `GRYLJ` decimal(18, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '上月所得算税数据记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_fee
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_fee`;
CREATE TABLE `t_merchant_fee`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '计费月份',
  `PERSON_REPORT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '采集人数',
  `REPORT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '计费人数',
  `SERVYOU_FEE_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税友计费',
  `INTERFACE_SERVICE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '当前使用的接口服务',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商户计费' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_merchant_info
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant_info`;
CREATE TABLE `t_merchant_info`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` int(11) NULL DEFAULT NULL COMMENT '商户id',
  `MERCHANT_NAME` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '企业名称',
  `GATEWAY_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '网关服务id',
  `INTERFACE_SERVICE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '接口服务',
  `PRODUCT_SERVICE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品服务',
  `SERVICE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务状态',
  `NOTIFY_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异步通知地址',
  `END_TIME` timestamp NULL DEFAULT NULL COMMENT '关停时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `START_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开启时间',
  `CELL_PHONE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `EMAIL` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `MERCHANT_ID`(`MERCHANT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_batch
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_batch`;
CREATE TABLE `t_pay_batch`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `BUSINESS_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求业务类型',
  `APPLY_CERTIFICATION_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应征凭证序号',
  `UNIQUE_IDENTIFY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '唯一识别号',
  `ELECTRONIC_TAX_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子税票号码',
  `TAX_OPERATION_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银税操作状态',
  `TAX_OPERATION_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银税操作名称',
  `INCOME_MONTH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得月份',
  `DECLARE_FORM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表列别',
  `TAX_PAY_START_TIME` date NULL DEFAULT NULL COMMENT '税款所属期起',
  `TAX_PAY_END_TIME` date NULL DEFAULT NULL COMMENT '税款所属期止',
  `PAID_IN_TAX_AMOUNT` decimal(10, 2) NULL DEFAULT NULL COMMENT '实缴税额',
  `PAID_IN_TAX_AMOUNT_TOTAL` decimal(10, 2) NULL DEFAULT NULL COMMENT '实缴税额汇总',
  `RESUB_OR_REFUND_AMOUNT` decimal(10, 2) NULL DEFAULT NULL COMMENT '应补（退）税额',
  `RESUB_OR_REFUND_AMOUNT_TOTAL` decimal(10, 2) NULL DEFAULT NULL COMMENT '应补（退）税额汇总',
  `PAY_DEADLINE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款期限',
  `PAY_DATE` date NULL DEFAULT NULL COMMENT '缴款日期',
  `PAY_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款状态',
  `PAY_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款信息',
  `PAY_SYNC_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款反馈同步标志',
  `TRIPLE_AGREEMENT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议号',
  `ASYNC_REQUEST_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异步流水号',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `START_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开启时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_PAYBATCH_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '缴款批次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_pay_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_pay_detail`;
CREATE TABLE `t_pay_detail`  (
  `ID` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `PAY_BATCH_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `APPLY_CERTIFICATION_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应征凭证序号',
  `UNIQUE_IDENTIFY_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '唯一识别号',
  `APPLY_CERTIFICATION_DETAIL_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `RESUB_OR_REFUND_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `COLLECTION_TREASURY_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收款国库代码',
  `LEVY_TAX_OFFICE_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征税税务机关代码',
  `LEVY_TAX_ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收项目代码',
  `LEVY_TAX_ITEM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收项目名称',
  `LEVY_TAX_PART_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收品目代码',
  `LEVY_TAX_PART_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '征收品目名称',
  `TAX_RATE` decimal(10, 4) NULL DEFAULT NULL COMMENT '税率',
  `TAX_PAY_START_TIME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属期起',
  `TAX_PAY_END_TIME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属期止',
  `PAY_DEADLINE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款期限',
  `PAID_IN_TAX_AMOUNT` decimal(10, 2) NULL DEFAULT NULL,
  `TAX_ORG_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属税务机构代码',
  `TAX_ORG_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属税务机构名称',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `MERCHANT_ID` int(10) UNSIGNED NULL DEFAULT NULL,
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_PAYDETAIL_BATCH_ID`(`PAY_BATCH_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_person_report_qylb
-- ----------------------------
DROP TABLE IF EXISTS `t_person_report_qylb`;
CREATE TABLE `t_person_report_qylb`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商户请求流水号',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `NSRSBH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `DJXH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人登记序号',
  `ZGSWJGMC` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务机关名称',
  `DJZCMC` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登记注册名称',
  `LXDH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系电话',
  `LXDZ` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系地址',
  `DJRQ` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登记日期',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CWFZR` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '财务负责人',
  `KJYWRMC` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单位名称',
  `FRDB` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '法定代表人(或单位负责人)',
  `ZGSWSKFJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务所(科、分局)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `I_PERSONQYLB_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '报验户企业列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_reduction_item
-- ----------------------------
DROP TABLE IF EXISTS `t_reduction_item`;
CREATE TABLE `t_reduction_item`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `REDUCTION_ITEM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免事项类别',
  `NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `JMFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免方式',
  `SDXMDM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目代码',
  `JMSX` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免事项',
  `JMXZ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '减免性质',
  `JMSE` decimal(10, 2) NULL DEFAULT NULL COMMENT '减免税额、免税收入',
  `SYSBM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税友识别码',
  `BDSXRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '保单生效日期',
  `NDBF` decimal(10, 2) NULL DEFAULT NULL COMMENT '年度保费',
  `YDBF` decimal(10, 2) NULL DEFAULT NULL COMMENT '月度保费',
  `BQKCJE` decimal(10, 2) NULL DEFAULT NULL COMMENT '本期扣除金额',
  `SYYLZHBH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税延养老账号编号',
  `SBKCYF_START` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报扣除月份起',
  `SBKCYF_END` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报扣除月份止',
  `BSJYM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报税校验码',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_REDUCTION_RECORD_IDNO`(`REQUEST_RECORD_ID`, `ID_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '减免事项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_report_info
-- ----------------------------
DROP TABLE IF EXISTS `t_report_info`;
CREATE TABLE `t_report_info`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `REPORT_FROM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `AMOUNT_COUNT` decimal(18, 2) NULL DEFAULT NULL COMMENT '总金额',
  `PEOPLE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '总人数',
  `YNSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '应纳税额',
  `SBBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报标志',
  `SBSJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报时间',
  `FKSJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈时间',
  `SBFKBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报反馈标志',
  `SBFKXX` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '申报反馈信息',
  `KKFKBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '扣款反馈标志',
  `KKFKXX` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '扣款反馈信息',
  `ERROR_INFO` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '申报表信息',
  `ZFBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '作废标志',
  `ZFPCH` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '作废批次号',
  `ZF_ERROR_INFO` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '作废申报表信息',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `YBTSE` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '申报信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_report_record
-- ----------------------------
DROP TABLE IF EXISTS `t_report_record`;
CREATE TABLE `t_report_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `BUSINESS_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `MONTH_DATE` date NULL DEFAULT NULL,
  `REPORT_FROM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `AMOUNT_COUNT` decimal(18, 2) NULL DEFAULT NULL,
  `PEOPLE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL,
  `YNSE` decimal(18, 2) NULL DEFAULT NULL,
  `SBBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SBSJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报时间',
  `FKSJ` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈时间',
  `SBFKBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `SBFKXX` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `KKFKBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `KKFKXX` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `ERROR_INFO` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `ZFBZ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZFPCH` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ZF_ERROR_INFO` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `YBTSE` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_REPORT_RECORD_FORM`(`REQUEST_RECORD_ID`, `REPORT_FROM_TYPE`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '申报信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_request_record
-- ----------------------------
DROP TABLE IF EXISTS `t_request_record`;
CREATE TABLE `t_request_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `MERCHANT_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商户名称',
  `REQUEST_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `AREA_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '行政区划代码',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `DEPARTMENT_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门编号',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '版本',
  `SMZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实名账号',
  `SMMM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '实名密码',
  `YZSMZH` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '验证实名账号',
  `DJXHID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主管税务机关登记序号id',
  `SBMM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报密码',
  `ADDITION_DEDUCT_AUTO_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '专项自动扣除',
  `SFCSLJKC` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否重算累计扣除',
  `QKBSSBRY` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清空报送失败人员',
  `CHANNEL_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通道:SERVYOU-税友接口,SELF_SERVYOU-自有模式',
  `BUSINESS_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主业务类型 SubtaxBizTypeEnum',
  `ADDITION_DOWNLOAD_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载专项信息',
  `TOTAL_DOWNLOAD_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载累计数据',
  `NEW_COMPANY_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '新设立企业',
  `PEOPLE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '总人数',
  `AMOUNT_COUNT` decimal(18, 2) NULL DEFAULT NULL COMMENT '总金额',
  `BBHZFH` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表汇总返回',
  `REPORT_FROM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `TRIPLE_AGREEMENT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议号',
  `NOTIFY_URL` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调地址',
  `ACCEPT_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '受理id',
  `RELATION_ACCEPT_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联受理id',
  `DEAL_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理状态:INIT,ACCEPT,PRE_PROCESSING,PROCESSING,SUCCESS,FAIL,ERROR',
  `FEE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收费状态:CHARGE，NO_CHARGE',
  `ERROR_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误码',
  `ERROR_MSG` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `CLIENT_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客户端key',
  `SDYF_START` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得月分起',
  `SDYF_END` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得月分止',
  `PRODUCT_SERVICE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品:XST-薪税通,API-API接口',
  `SHORT_SERVICE_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '网关短服务名',
  `SYSTEM_ACCEPT_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '系统受理id',
  `SYSTEM_RELATION_ACCEPT_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '系统关联受理id',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_ACCEPT_ID`(`ACCEPT_ID`) USING BTREE,
  INDEX `IDX_MER_REQUEST_NO`(`MERCHANT_ID`, `REQUEST_NO`) USING BTREE,
  INDEX `IDX_SYSTEM_ACCEPT_ID`(`SYSTEM_ACCEPT_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '请求记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sum_tax_calculate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sum_tax_calculate_record`;
CREATE TABLE `t_sum_tax_calculate_record`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `ZNJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '子女教育支出',
  `SYLRZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '赡养老人支出',
  `ZFDKLXZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房贷款利息支出',
  `ZFZJZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房租金支出',
  `DBYLZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '大病医疗支出',
  `JXJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '继续教育支出',
  `YYEZHFZC` decimal(18, 2) NULL DEFAULT NULL,
  `LJSRE` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计收入',
  `LJMSSR` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计免税收入',
  `JCFY` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计减除费用',
  `ZXKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项扣除合计',
  `QTCKHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计其他扣除',
  `LJZYKCDJZE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计准予扣除的捐赠额',
  `ZXFJKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项附加扣除合计',
  `KCXMHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '扣除项目合计',
  `LJYNSSDE` decimal(18, 2) NULL DEFAULT NULL,
  `SL` decimal(18, 2) NULL DEFAULT NULL,
  `SSKCS` decimal(18, 2) NULL DEFAULT NULL,
  `LJYNSE` decimal(18, 2) NULL DEFAULT NULL,
  `LJJMSE` decimal(18, 2) NULL DEFAULT NULL,
  `LJYINGKJSE` decimal(18, 2) NULL DEFAULT NULL,
  `YKJSE` decimal(18, 2) NULL DEFAULT NULL,
  `YBTSE` decimal(18, 2) NULL DEFAULT NULL,
  `CALCULATE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_SUMTAX_RECORD_IDNO`(`REQUEST_RECORD_ID`, `ID_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '累计算税数据下载记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_support_old
-- ----------------------------
DROP TABLE IF EXISTS `t_support_old`;
CREATE TABLE `t_support_old`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ERROR_INFO` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细错误信息',
  `SBBZ` int(11) NULL DEFAULT NULL COMMENT '申报标记',
  `FKXX` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '明细申报反馈信息',
  `CJLY` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '采集来源',
  `XM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '被赡养/共同赡养人姓名',
  `ZZLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '被赡养/共同赡养人证照类型',
  `ZZHM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '被赡养/共同赡养人证照号码',
  `GJDQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '被赡养/共同赡养人国籍和地区',
  `YNSRGX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '与纳税人关系',
  `CSRQ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '被赡养/共同赡养人出生日期',
  `CZZT` int(11) NULL DEFAULT NULL COMMENT '共同赡养人操作状态',
  `FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '赡养人/共同赡养人标识',
  `DEDUCT_BACK_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '专项申报反馈主表id',
  `GXSJ` date NULL DEFAULT NULL,
  `KCYXQQ` date NULL DEFAULT NULL,
  `KCYXQZ` date NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `T_SUPPORT_OLD_DEDUCT_BACK_ID_index`(`DEDUCT_BACK_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '赡养老人' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_lock`;
CREATE TABLE `t_sys_lock`  (
  `NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `OWNER` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '持有者',
  `LOCKED_TIME` datetime NULL DEFAULT NULL COMMENT '加锁时间',
  `REMARK` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`NAME`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sys_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_mq_log`;
CREATE TABLE `t_sys_mq_log`  (
  `MOST_BITS` bigint(20) NOT NULL,
  `LEAST_BITS` bigint(20) NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `EXCHANGE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `ROUTING_KEY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `PAYLOAD` blob NULL,
  PRIMARY KEY (`MOST_BITS`, `LEAST_BITS`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_calculate_result
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_calculate_result`;
CREATE TABLE `t_tax_calculate_result`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '所得月份',
  `EMP_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '姓名',
  `ID_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照类型',
  `ID_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证照号码',
  `REPORT_FROM_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `ITEM_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目',
  `DELETE_HISTORY_YN` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否删除历史数据',
  `SFHZSB` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否汇总申报',
  `SRE` decimal(18, 2) NULL DEFAULT NULL COMMENT '当期收入额',
  `MSSD` decimal(18, 2) NULL DEFAULT NULL COMMENT '当期免税收入',
  `ZYKCJZE` decimal(18, 2) NULL DEFAULT NULL COMMENT '准予扣除捐赠额',
  `JBYLAOBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '基本养老保险',
  `JBYLBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '基本医疗保险',
  `SYBXF` decimal(18, 2) NULL DEFAULT NULL COMMENT '失业保险',
  `ZFGJJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房公积金',
  `ZNJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '子女教育支出',
  `SYLRZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '赡养老人支出',
  `ZFDKLXZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房贷款利息支出',
  `ZFZJZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '住房租金支出',
  `DBYLZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '大病医疗支出',
  `JXJYZC` decimal(18, 2) NULL DEFAULT NULL COMMENT '继续教育支出',
  `YYEZHFZC` decimal(18, 2) NULL DEFAULT NULL,
  `NJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '年金',
  `SYJKBX` decimal(18, 2) NULL DEFAULT NULL COMMENT '商业健康保险',
  `SYYLBX` decimal(18, 2) NULL DEFAULT NULL COMMENT '税延养老保险',
  `QT` decimal(18, 2) NULL DEFAULT NULL COMMENT '其他',
  `YXKCSF` decimal(18, 2) NULL DEFAULT NULL COMMENT '允许扣除的税费',
  `JMSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '减免税额',
  `LJSRE` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计收入',
  `LJFY` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计费用',
  `LJMSSR` decimal(18, 2) NULL DEFAULT NULL COMMENT '上期累计免税收入',
  `JCFY` decimal(18, 2) NULL DEFAULT NULL COMMENT '减除费用',
  `ZXKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项扣除合计',
  `QTCKHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计其他扣除',
  `LJZYKCDJZE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计准予扣除的捐赠额',
  `ZXFJKCHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计专项附加扣除合计',
  `KCXMHJ` decimal(18, 2) NULL DEFAULT NULL COMMENT '扣除项目合计',
  `FY` decimal(18, 2) NULL DEFAULT NULL COMMENT '费用',
  `ZYCB` decimal(18, 2) NULL DEFAULT NULL COMMENT '展业成本',
  `LJYNSSDE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应纳税所得额',
  `SL` decimal(18, 4) NULL DEFAULT NULL COMMENT '税率',
  `SSKCS` decimal(18, 2) NULL DEFAULT NULL COMMENT '速算扣除数',
  `LJYNSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应纳税额',
  `LJJMSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计减免税额',
  `LJYINGKJSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应扣缴税额',
  `YKJSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '已预交税额',
  `YBTSE` decimal(18, 2) NULL DEFAULT NULL COMMENT '应补退税额',
  `CALCULATE_STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '算税状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `REQUEST_NO` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  `SDXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得项目代码',
  `SJJZE` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际捐赠额',
  `JZFS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '捐赠方式',
  `BZ` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `SYGS` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '适用公式',
  `JNTS` decimal(10, 2) NULL DEFAULT NULL COMMENT '境内工作天数',
  `JWTS` decimal(10, 2) NULL DEFAULT NULL COMMENT '境外工作天数',
  `JNZF` decimal(10, 2) NULL DEFAULT NULL COMMENT '境内支付金额',
  `JWZF` decimal(10, 2) NULL DEFAULT NULL COMMENT '境外支付金额',
  `LJJCFY` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计减除费用',
  `NCXYS` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '农村信用社',
  `JJBL` int(11) NULL DEFAULT NULL COMMENT '减记比例',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_TAX_RECORD_IDNO`(`REQUEST_RECORD_ID`, `ID_NO`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '算税数据记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_payment_certificate_file
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_payment_certificate_file`;
CREATE TABLE `t_tax_payment_certificate_file`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TAX_PAYMENT_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '完税证明记录表ID',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `FILE_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件名',
  `FILE` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载文件',
  `ERROR_INFO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
  `VERSION` int(11) NULL DEFAULT NULL COMMENT '版本',
  `CREATED_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATED_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_T_TAX_FILE_RECORD_ID`(`TAX_PAYMENT_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '完税证明文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_tax_payment_certificate_record
-- ----------------------------
DROP TABLE IF EXISTS `t_tax_payment_certificate_record`;
CREATE TABLE `t_tax_payment_certificate_record`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) NULL DEFAULT NULL COMMENT '商户id',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) NULL DEFAULT NULL COMMENT '请求记录id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `STATUS` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载状态',
  `ERROR_INFO` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '下载信息',
  `MONTH_DATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所得月份',
  `REPORT_FROM_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表名称',
  `SBRC` int(11) NULL DEFAULT NULL COMMENT '申报人次',
  `DQSRE` decimal(32, 10) NULL DEFAULT NULL COMMENT '当期收入',
  `YBTSE` decimal(32, 10) NULL DEFAULT NULL COMMENT '应补退税额',
  `SBLX` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报类型',
  `REPORT_FROM_TYPE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '报表类别',
  `SBQD` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报渠道',
  `SBSJ` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申报时间',
  `VERSION` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `CREATED_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATED_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_TAX_PAYMENT_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_taxsub_fee
-- ----------------------------
DROP TABLE IF EXISTS `t_taxsub_fee`;
CREATE TABLE `t_taxsub_fee`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税识别号',
  `MONTH_DATE` date NULL DEFAULT NULL COMMENT '计费月份',
  `PERSON_REPORT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '采集人数',
  `REPORT_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '申报人数',
  `REPORT_FALG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否申报',
  `FEE_COUNT` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '计费人数',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '扣缴义务人计费' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_triple_agreement
-- ----------------------------
DROP TABLE IF EXISTS `t_triple_agreement`;
CREATE TABLE `t_triple_agreement`  (
  `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `MERCHANT_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '商户id',
  `TAX_PAYER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '纳税人识别号',
  `REQUEST_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求流水号',
  `REQUEST_RECORD_ID` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '请求记录id',
  `VERSION` int(10) UNSIGNED NULL DEFAULT 0 COMMENT 'VERSION',
  `TRIPLE_AGREEMENT_UUID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议UUID',
  `REGISTRATION_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登记序号',
  `TAX_ORG_COED` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属税务机构代码',
  `TRIPLE_AGREEMENT_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议号',
  `YHHB_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行行别代码',
  `YHHB_NAME` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行行别名称',
  `YHYYWD_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行营业网点代码',
  `AREA_NUM` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '行政区划数字代码',
  `CLEARINGG_BANK_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '清算行行号',
  `ACCOUNT_BANK_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开户行行号',
  `PAY_ACCOUNT` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款账户',
  `PAY_ACCOUNT_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '缴款账户名称',
  `TRIPLE_AGREEMENT_STATUS_CODE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议状态代码',
  `TRIPLE_AGREEMENT_VERIFY_INFO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议验证信息',
  `TRIPLE_AGREEMENT_PASS_DATE` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '三方协议验证通过日期',
  `BATCH_PROXY_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '批扣标志',
  `DATA_SYNC_TIME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据同步时间',
  `UPDATE_TIME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '协议更新时间',
  `VALID_FLAG` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否有效',
  `TAX_ORG_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税款所属税务机构名称',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `I_TRIPLE_RECORD_ID`(`REQUEST_RECORD_ID`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '三方协议表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
